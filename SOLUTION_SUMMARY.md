# 多数据源事务问题解决方案

## 问题描述
在 `DeviceBoundServiceImpl.bindDeviceAction` 方法中，`deviceListService` 和 `deviceListDefaultService` 分别配置了不同的数据源：
- `deviceListService` → `@DS("ecos-local")` 数据源
- `deviceListDefaultService` → `@DS("ecos-admin")` 数据源

但是在 `com.weihengtech.controller.common.device.DeviceBindController#iotNetBindAccount` 方法执行过程中，`deviceListDefaultService` 数据源并未生效，原因是 `@Transactional` 注解的限制。

## 根本原因
Spring 的 `@Transactional` 注解默认只能管理单一数据源的事务。当在同一个事务中访问多个数据源时：
1. 只有第一个访问的数据源会被事务管理器管理
2. 后续访问的数据源不会加入到当前事务中
3. 导致多数据源操作无法保证事务一致性

## 解决方案
使用 `@DSTransactional` 注解替换 `@Transactional` 注解来支持多数据源事务管理。

### 修改内容

#### 1. 修改 `iotNetDeviceBind` 方法
```java
// 修改前
@Transactional(rollbackFor = Exception.class)
public NetBindDeviceDTO iotNetDeviceBind(NetBindWhVO netBindDeviceVo) {

// 修改后  
@DSTransactional
public NetBindDeviceDTO iotNetDeviceBind(NetBindWhVO netBindDeviceVo) {
```

#### 2. 修改 `netDeviceBind` 方法
```java
// 修改前
@Transactional(rollbackFor = Exception.class)
public NetBindDeviceDTO netDeviceBind(NetBindDeviceVO netBindDeviceVo) {

// 修改后
@DSTransactional  
public NetBindDeviceDTO netDeviceBind(NetBindDeviceVO netBindDeviceVo) {
```

#### 3. 添加必要的导入
```java
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
```

## 技术说明

### @DSTransactional 注解特性
- 由 dynamic-datasource 框架提供
- 支持多数据源事务管理
- 自动处理跨数据源的事务一致性
- 默认包含异常回滚机制（等同于 `rollbackFor = Exception.class`）

### 数据源配置
```properties
# 主数据源
spring.datasource.dynamic.primary=ecos-local

# 数据源配置
spring.datasource.dynamic.datasource.ecos-local.url=...
spring.datasource.dynamic.datasource.ecos-admin.url=...
```

### Mapper 数据源绑定
```java
@DS("ecos-local")
public interface DeviceListMapper extends BaseMapper<DeviceListDO> {}

@DS("ecos-admin") 
public interface DeviceListDefaultMapper extends BaseMapper<DeviceListDefaultDO> {}
```

## 验证方法
1. 启动应用程序
2. 调用 `iotNetBindAccount` 接口
3. 观察日志确认两个数据源都能正常访问
4. 验证事务回滚机制是否正常工作

## 注意事项
1. `@DSTransactional` 注解已经包含了异常回滚机制，无需额外指定 `rollbackFor`
2. 确保项目中已经正确配置了 dynamic-datasource 依赖
3. 多数据源事务会有一定的性能开销，建议在必要时使用

## 相关文件
- `hub-core/src/main/java/com/weihengtech/service/device/impl/DeviceBoundServiceImpl.java`
- `hub-core/src/main/java/com/weihengtech/dao/device/DeviceListMapper.java`  
- `hub-core/src/main/java/com/weihengtech/dao/device/DeviceListDefaultMapper.java`
