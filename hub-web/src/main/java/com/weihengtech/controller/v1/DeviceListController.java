package com.weihengtech.controller.v1;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.EmptyResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.common.exceptions.RoleException;
import com.weihengtech.consts.EnumConstants;
import com.weihengtech.consts.RecordContentConstants;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.pojo.bos.device.DeviceListPageBo;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.firmware.FirmwareUploadDO;
import com.weihengtech.pojo.dtos.device.*;
import com.weihengtech.pojo.dtos.firmware.UpgradeResDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.vos.device.*;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpgradeVO;
import com.weihengtech.pojo.vos.firmware.SoftwareVersionUpgradeWithSnVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceRemarkService;
import com.weihengtech.service.firmware.FirmwareUpgradeService;
import com.weihengtech.service.firmware.FirmwareUploadService;
import com.weihengtech.service.other.RetryService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("hybridSinglePhase")
@Api(tags = "01_设备列表")
public class DeviceListController {

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private RetryService retryService;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private DeviceRemarkService deviceRemarkService;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private FirmwareUploadService firmwareUploadService;
	@Resource
	private FirmwareUpgradeService firmwareUpgradeService;

	@PostMapping("page")
	@ApiOperation(value = "设备信息分页")
	public DataResponse<PageInfoDTO<DeviceListPageDTO>> pageDeviceList(@RequestBody @Valid DeviceListPageVO param) {
		param.checkParams();
		DeviceListPageBo queryParam = new DeviceListPageBo();
		CglibUtil.copy(param, queryParam);
		PageInfoDTO<DeviceListPageDTO> res = deviceListService.pageDeviceList(queryParam,
				"DEVICE_LIST");
		return DataResponse.success(res);
	}

	@PostMapping("pageSingleInput")
	@ApiOperation(value = "单输入框设备信息分页")
	public DataResponse<PageInfoDTO<DeviceListPageDTO>> pageDeviceListSingleInput(@RequestBody @Valid DeviceSingleInputPageVo param) {
		param.checkParams();
		DeviceListPageBo queryParam = new DeviceListPageBo();
		CglibUtil.copy(param, queryParam);
		PageInfoDTO<DeviceListPageDTO> res = deviceListService.pageDeviceList(queryParam,
				"SINGLE_DEVICE_LIST");
		return DataResponse.success(res);
	}

	@PostMapping("/storage/page")
	@ApiOperation(value = "储能设备分页")
	public DataResponse<PageInfoDTO<DeviceListPageDTO>> storageList(@RequestBody @Valid DeviceCommonPageVo param) {
		param.checkParams();
		DeviceListPageBo queryParam = new DeviceListPageBo();
		CglibUtil.copy(param, queryParam);
		PageInfoDTO<DeviceListPageDTO> res = deviceListService.storageList(queryParam, "STORAGE_DEVICE_LIST");
		return DataResponse.success(res);
	}

	@GetMapping("detail")
	@ApiOperation(value = "查询储能机扩展详情")
	public DataResponse<DeviceListDO> deviceDetail(@RequestParam Long id) {
		DeviceListDO deviceInfo = deviceListService.getById(id);
		if (2 != UserInfoUtil.currentUser().getSource()) {
			deviceInfo.buildDefaultVersion();
		}
		return DataResponse.success(deviceInfo);
	}

	@PostMapping("/statistics")
	@ApiOperation(value = "设备信息统计")
	public DataResponse<DeviceStatisticsDTO> statistics() {
		DeviceStatisticsDTO res = deviceListService.statistics();
		return DataResponse.success(res);
	}

	@PostMapping("software/upgrade")
	@ApiOperation(value = "固件软件版本升级")
	@ApiIgnore
	public EmptyResponse upgradeSoftwareVersion(
			@RequestBody @Valid SoftwareVersionUpgradeVO softwareVersionUpgradeVO
	) {
		firmwareUpgradeService.upgradeSoftware(softwareVersionUpgradeVO);
		// tag
		OperatingRecordUtil.log(Long.parseLong(UserInfoUtil.currentUserId()), RecordContentConstants.UPGRADE_FIRMWARE,
				MapUtil.<String, String>builder()
						.put("deviceIdList", JSONUtil.toJsonStr(softwareVersionUpgradeVO.getDeviceIdList()))
						.put("firmwareIdList", JSONUtil.toJsonStr(softwareVersionUpgradeVO.getFirmwareIdList()))
						.build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()
		);
		return EmptyResponse.success();
	}

	@PostMapping("software/upgradeWithSn")
	@ApiOperation(value = "使用Sn批量升级固件")
	public DataResponse<UpgradeResDTO> upgradeSoftwareVersionWithSn(
			@RequestBody @Valid SoftwareVersionUpgradeWithSnVo softwareVersionUpgradeWithSnVo
	) {
		UpgradeResDTO res = firmwareUpgradeService.upgradeSoftwareWithSn(softwareVersionUpgradeWithSnVo);
		// tag
		Collection<FirmwareUploadDO> list = firmwareUploadService.listByIds(softwareVersionUpgradeWithSnVo.getFirmwareIdList());
		OperatingRecordUtil.log(RecordContentConstants.UPGRADE_FIRMWARE_SN, MapUtil.<String, String>builder()
						.put(
								"deviceNameList",
								JSONUtil.toJsonStr(
										softwareVersionUpgradeWithSnVo.getDeviceNameList())
						)
						.put(
								"firmwareIdList",
								JSONUtil.toJsonStr(
										list.stream().map(FirmwareUploadDO::getFirmwareName).collect(Collectors.toList()))
						).build(),
				EnumConstants.RecordModule.FIRMWARE_MAINTAIN.getCode()

		);
		return DataResponse.success(res);
	}

	@PostMapping("handle")
	@ApiOperation(value = "设备操作 0: 开机 1: 关机 2: 重启")
	public EmptyResponse handleDeviceState(
			@RequestBody @Valid DeviceStateHandleVO deviceStateHandleVO) {
		List<String> deviceNameList = deviceStateHandleVO.getDeviceNameList();
		String roleId = UserInfoUtil.currentUserRole().getRoleId();
		String userId = UserInfoUtil.currentUserId();
		if (!RoleConstants.ROLE_ADMIN.equals(roleId) && deviceNameList.size() > 1) {
			throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
		}
		// tag
		switch (deviceStateHandleVO.getAction()) {
			case "0":
				OperatingRecordUtil.log(Long.parseLong(userId), RecordContentConstants.START_DEVICE,
						MapUtil.<String, String>builder()
								.put("deviceNameList", JSONUtil.toJsonStr(deviceNameList))
								.build(),
						EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
				);
				break;
			case "1":
				OperatingRecordUtil.log(Long.parseLong(userId), RecordContentConstants.DOWN_DEVICE,
						MapUtil.<String, String>builder()
								.put("deviceNameList", JSONUtil.toJsonStr(deviceNameList))
								.build(),
						EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
				);
				break;
			case "2":
				OperatingRecordUtil.log(Long.parseLong(userId), RecordContentConstants.RESTART_DEVICE,
						MapUtil.<String, String>builder()
								.put("deviceNameList", JSONUtil.toJsonStr(deviceNameList))
								.build(),
						EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
				);
				break;
			default:
		}
		threadPoolTaskExecutor.execute(() -> deviceListService.handleDeviceState(deviceStateHandleVO));
		return EmptyResponse.success();
	}

	@GetMapping("like/sn")
	@ApiOperation(value = "模糊查询设备名")
	public DataResponse<List<DeviceLikeSnDTO>> likeDeviceSn(
			@RequestParam("deviceName") String deviceName
	) {
		if (StrUtil.isBlank(deviceName)) {
			return DataResponse.success(ListUtil.empty());
		}
		return DataResponse.success(deviceListService.likeDeviceSn(deviceName));
	}

	@GetMapping("like/deviceName")
	@ApiOperation(value = "模糊查询设备名")
	public DataResponse<List<String>> likeDeviceName(
			@RequestParam("deviceName") String deviceName
	) {
		if (StrUtil.isBlank(deviceName)) {
			return DataResponse.success(ListUtil.empty());
		}
		return DataResponse.success(deviceListService.likeDeviceName(deviceName));
	}

	@GetMapping("like/wifiSn")
	@ApiOperation(value = "模糊查询wifiSn")
	public DataResponse<List<String>> likeWifiSn(@RequestParam("wifiSn") String wifiSn) {
		if (StrUtil.isBlank(wifiSn)) {
			return DataResponse.success(ListUtil.empty());
		}
		return DataResponse.success(deviceListService.likeWifiSn(wifiSn));
	}

	@GetMapping("like/alias")
	@ApiOperation(value = "模糊查询备注")
	public DataResponse<List<String>> likeAlias(@RequestParam("alias") String alias) {
		if (StrUtil.isBlank(alias)) {
			return DataResponse.success(ListUtil.empty());
		}
		return DataResponse.success(deviceListService.likeAlias(alias));
	}

	@GetMapping("like/desc")
	@ApiOperation(value = "模糊查询描述")
	public DataResponse<List<String>> likeDesc(@RequestParam("desc") String desc) {
		if (StrUtil.isBlank(desc)) {
			return  DataResponse.success(ListUtil.empty());
		}
		return DataResponse.success(deviceListService.likeDesc(desc));
	}

	@PostMapping("syncState")
	@ApiOperation(value = "同步单设备状态")
	public DataResponse<Integer> syncDeviceState(@RequestBody @Valid DeviceSyncStateVo deviceSyncStateVo) {
		return DataResponse.success(deviceListService.syncDeviceState(deviceSyncStateVo));
	}

	@PostMapping("syncStateAndSoc")
	@ApiOperation(value = "同步单设备状态和soc")
	public DataResponse<DeviceStatusAndSocDTO> syncDeviceStateAndSoc(@RequestBody @Valid DeviceSyncStateVo deviceSyncStateVo) {
		return DataResponse.success(deviceListService.syncDeviceStateAndSoc(deviceSyncStateVo));
	}

	@PostMapping("alias")
	@ApiOperation(value = "修改设备别名")
	public EmptyResponse updateDeviceAlias(@RequestBody @Valid DeviceAliasUpdateVo deviceAliasUpdateVo) {
		deviceListService.updateDeviceAlias(deviceAliasUpdateVo);
		return EmptyResponse.success();
	}

	@PostMapping("remark")
	@ApiOperation(value = "修改设备备注")
	public EmptyResponse updateDeviceRemark(
			@RequestBody @Valid DeviceRemarkUpdateVo deviceRemarkUpdateVo
	) {
		deviceRemarkService.updateByDeviceIdAndAccountId(
				deviceRemarkUpdateVo.getDeviceId(),
				deviceRemarkUpdateVo.getRemark());
		return EmptyResponse.success();
	}

	// 修改设备描述
	@PostMapping("description")
	@ApiOperation(value = "修改设备描述")
	public EmptyResponse updateDeviceDescription(
			@RequestBody @Valid DeviceDescUpdateVo deviceDescUpdateVo
	) {
		deviceListService.updateDeviceDescription(deviceDescUpdateVo);
		return EmptyResponse.success();
	}

	@PostMapping("batchNewDevice")
	@ApiOperation(value = "批量新增设备")
	public DataResponse<BatchNewDeviceDto> batchNewDevice(
			@RequestBody @Valid BatchNewDeviceVo batchNewDeviceVo
	) {
		return DataResponse.success(deviceListService.batchNewDevice(batchNewDeviceVo));
	}

	@PostMapping("syncDeviceVersion")
	@ApiOperation(value = "同步设备版本号")
	public DataResponse<DeviceSyncVersionDto> syncDeviceVersion(@RequestBody @Valid DeviceIdVo deviceIdVo) {
		DeviceListDO deviceListDO = deviceListService.getDeviceById(Long.parseLong(deviceIdVo.getDeviceId()));
		BeanUtil.assertNotNull(deviceListDO);
		DeviceSyncVersionDto item = retryService.syncDeviceVersion(deviceListDO, false);
		if (2 != UserInfoUtil.currentUser().getSource()) {
			item.buildDefaultVersion();
		}
		return DataResponse.success(item);
	}

	@GetMapping("getById")
	@ApiIgnore
	@ApiOperation(value = "使用设备Id获取设备实例")
	public DataResponse<DeviceListDO> getDeviceInfoById(@RequestParam("deviceId") Long deviceId) {
		return DataResponse.success(deviceListService.getDeviceById(deviceId));
	}

	@GetMapping("getAgentId")
	@ApiOperation(value = "获取代理商id")
	@ApiIgnore
	public DataResponse<String> getAgentId(@RequestParam("deviceId") String deviceId) {
		return DataResponse.success(deviceListService.getAgentId(deviceId));
	}

	@GetMapping("delay_queue/operate")
	@ApiOperation(value = "重启监听升级线程")
	@ApiIgnore
	public DataResponse<String> reloadListen(@RequestParam(required = false) String type) {
		String msg = firmwareUpgradeService.upgradeCurDevice(type);
		return DataResponse.success(msg);
	}

	@PostMapping("like/sn/page")
	@ApiOperation(value = "sn模糊搜索分页")
	public DataResponse<PageInfoDTO<DeviceLikeSnDTO>> snLikePage(@RequestBody DeviceLikePageVo pageVo) {
		PageInfoDTO<DeviceLikeSnDTO> pageInfo = deviceListService.snLikePage(pageVo);
		return DataResponse.success(pageInfo);
	}

	@PostMapping("updateAddress")
	@ApiOperation(value = "更改设备详细地址")
	public EmptyResponse updateDeviceAddress(@RequestBody @Valid DeviceAddressUpdateVo deviceAddressUpdateVo) {
		deviceListService.updateDeviceAddress(deviceAddressUpdateVo);
		return EmptyResponse.success();
	}
}
