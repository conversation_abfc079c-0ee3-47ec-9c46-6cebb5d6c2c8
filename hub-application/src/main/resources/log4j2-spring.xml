<?xml version="1.0" encoding="UTF-8"?>
<!--设置log4j2的自身log级别为warn -->
<configuration status="warn">
    <properties>
        <!--这里配置的是日志存到文件中的路径-->
        <Property name="log_path">logs</Property>
        <Property name="server_name">service</Property>
        <!--简化的日志格式，减少磁盘占用-->
        <Property name="pattern_simple">[%d{yyyy-MM-dd HH:mm:ss}][%p][%t] %c{1}: %m%n</Property>
        <Property name="pattern_error">[%d{yyyy-MM-dd HH:mm:ss}][%p][%t] %c{1}#%L: %m%n</Property>
    </properties>
    <appenders>
        <!--输出格式布局，每个转换说明符以百分号(%)开头，'%'后面的转换字符有如下:-->
        <!--
        p (level) 日志级别
        c（logger） Logger的Name
        C (class) Logger调用者的全限定类名 ***
        d (date) 日期
        highlight 高亮颜色
        l (location) 调用位置 ***
        L (line) 行号
        m (msg/message) 输出的内容
        M (methode) 调用方法 ***
        maker marker的全限定名
        n 输出平台相关的换行符,如'\n' '\r\n'
        pid (processId) 进程ID
        level （p）日志级别
        r JVM启动后经过的微秒
        t (tn/thread/threadName) 线程名称
        T (tid/threadId) 线程ID
        tp (threadPriority) 线程优先级
        x (NDC) 线程Context堆栈
        -->
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="[%style{%d{yyyy-MM-dd HH:mm:ss.SSS}}{bright_white}][%highlight{%p}][%style{%t}{Yellow}][%style{%c{1.1.1.*}#%L}{Cyan}] %style{%m%n}{bright_white}"
                    disableAnsi="false"/>
        </console>
        <!-- 合并INFO和WARN级别日志，启用压缩和清理策略 -->
        <RollingFile name="RollingFileApp" fileName="${log_path}/${server_name}/app.log"
                     filePattern="${log_path}/${server_name}/$${date:yyyy-MM}/app-%d{yyyy-MM-dd}-%i.log.gz">
            <Filters>
                <ThresholdFilter level="INFO"/>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout pattern="${pattern_simple}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <!-- 保留策略：最多保留30天或100个文件 -->
            <DefaultRolloverStrategy max="100">
                <Delete basePath="${log_path}/${server_name}" maxDepth="2">
                    <IfFileName glob="*/app-*.log.gz"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
        <!-- 错误日志单独记录，包含详细信息用于问题排查 -->
        <RollingFile name="RollingFileError" fileName="${log_path}/${server_name}/error.log"
                     filePattern="${log_path}/${server_name}/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log.gz">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout pattern="${pattern_error}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <!-- 错误日志保留更长时间：60天或50个文件 -->
            <DefaultRolloverStrategy max="50">
                <Delete basePath="${log_path}/${server_name}" maxDepth="2">
                    <IfFileName glob="*/error-*.log.gz"/>
                    <IfLastModified age="60d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </appenders>
    <loggers>
        <!--过滤掉spring的一些无用的debug信息-->
        <logger name="org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport"
                level="Error"/>
        <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping"
                level="Error"/>
        <logger name="org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping"
                level="Error"/>
        <logger name="org.springframework.amqp.rabbit.connection.CachingConnectionFactory" level="Error"/>
        <logger name="org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer" level="Error"/>
        <logger name="org.springframework.boot.web.servlet.FilterRegistrationBean" level="Error"/>
        <logger name="org.springframework.jmx.export.annotation.AnnotationMBeanExporter" level="Error"/>
        <root level="INFO">
            <appender-ref ref="Console"/><!-- 配置控制台输出日志 -->
            <appender-ref ref="RollingFileApp"/><!-- 配置应用日志(INFO+WARN) -->
            <appender-ref ref="RollingFileError"/><!-- 配置错误日志 -->
        </root>
    </loggers>
</configuration>