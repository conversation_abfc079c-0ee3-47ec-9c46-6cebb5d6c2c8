package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.weihengtech.api.EcosClientApi;
import com.weihengtech.api.pojo.dtos.AccountBindInfoDTO;
import com.weihengtech.auth.dto.BindInfoDTO;
import com.weihengtech.auth.dto.BindQueryReqDTO;
import com.weihengtech.auth.dto.BindReqDTO;
import com.weihengtech.auth.dto.ResourceResDTO;
import com.weihengtech.auth.dto.VppBindReqDTO;
import com.weihengtech.auth.dto.VppBindResDTO;
import com.weihengtech.auth.model.bo.RoleInfo;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.DataResponse;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.EcosException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.common.exceptions.RoleException;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.enums.device.DeviceDatasourceEnum;
import com.weihengtech.enums.device.DeviceSaveTimeEnum;
import com.weihengtech.enums.device.ImportTypeEnum;
import com.weihengtech.enums.system.StepEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.device.DeviceListDefaultDO;
import com.weihengtech.pojo.dos.systeminfo.SystemInfoDO;
import com.weihengtech.pojo.dtos.device.DeviceBoundBindAccountDto;
import com.weihengtech.pojo.dtos.device.DeviceBoundUnBindAccountDto;
import com.weihengtech.pojo.dtos.device.NetBindDeviceDTO;
import com.weihengtech.pojo.dtos.ecos.InstallBoundDTO;
import com.weihengtech.pojo.dtos.ecos.InstallBoundInfoDTO;
import com.weihengtech.pojo.vos.device.DeviceBoundBindAccountVo;
import com.weihengtech.pojo.vos.device.DeviceBoundBindCountryVo;
import com.weihengtech.pojo.vos.device.NetBindDeviceVO;
import com.weihengtech.pojo.vos.device.NetBindWhVO;
import com.weihengtech.prometheus.BindFailMetrics;
import com.weihengtech.service.device.DeviceBoundService;
import com.weihengtech.service.device.DeviceListDefaultService;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.iot.IotClientService;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.service.other.RetryService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.systeminfo.SystemDeviceRelService;
import com.weihengtech.service.systeminfo.SystemInfoService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.InitUtil;
import com.weihengtech.utils.RotUtil;
import com.weihengtech.utils.SpecialParseUtil;
import com.weihengtech.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeviceBoundServiceImpl implements DeviceBoundService {

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private DeviceListService deviceListService;
	@Resource
	private EcosCountryService ecosCountryService;
	@Resource
	private SystemInfoService systemInfoService;
	@Resource
	private SystemDeviceRelService systemDeviceRelService;
	@Resource
	private RedissonClient redissonClient;
	@Resource
	private IotClientService whIotClient;
	@Resource
	private AuthCenterResClient authCenterResClient;
	@Resource
	private EcosClientApi ecosClientApi;
	@Resource
	private RetryService retryService;
	@Resource
	private ThreadPoolTaskExecutor threadPoolTaskExecutor;
	@Resource
	private BindFailMetrics bindFailMetrics;
	@Resource
	private DeviceListDefaultService deviceListDefaultService;

	private Map<Long, String> filterAndQueryDeviceByDeviceSnList(List<String> deviceSnList, List<String> invalidSnList, List<String> notExistSnList) {
		List<String> leaveSnList = deviceSnList.stream().filter(sn -> {
			if (StrUtil.isBlank(SpecialParseUtil.parseDeviceSn(ImportTypeEnum.storage.getCode(), sn))) {
				invalidSnList.add(sn);
				return false;
			}
			return true;
		}).collect(Collectors.toList());
		List<DeviceListDO> deviceList = deviceListService.list(Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getDeviceName, deviceSnList));
		List<String> snList = deviceList.parallelStream().map(DeviceListDO::getDeviceName).collect(Collectors.toList());
		leaveSnList.parallelStream().forEach(sn -> {
			if (!snList.contains(sn)) {
				notExistSnList.add(sn);
			}
		});
		return deviceList.parallelStream().collect(Collectors.toMap(DeviceListDO::getId, DeviceListDO::getDeviceName));
	}

	@Override
	public void deviceBindCountry(DeviceBoundBindCountryVo deviceBoundBindCountryVo) {
		Integer countryId = deviceBoundBindCountryVo.getCountryId();
		List<String> deviceSnList = deviceBoundBindCountryVo.getDeviceSnList();
		if (ecosCountryService.exist(countryId) && CollUtil.isNotEmpty(deviceSnList)) {
			deviceListService.updateDeviceCountry(countryId, deviceSnList);
		}
	}

	@DSTransactional
	@Override
	public NetBindDeviceDTO netDeviceBind(NetBindDeviceVO netBindDeviceVo) {
		Long userId = Long.parseLong(UserInfoUtil.currentUserId());
		String wifiSn = netBindDeviceVo.getWifiSn();

		RLock lock = redissonClient.getLock(wifiSn);
		try {
			if (lock.tryLock(10, TimeUnit.SECONDS)) {
				try {
					return netDeviceBind(netBindDeviceVo, userId);
				} catch (Exception e) {
					bindFailMetrics.recordFailure(wifiSn, "绑定最终失败");
					log.error(String.format("wifiSn:%s, userId:%s, systemId:%s, lon:%s, lat:%s bind failure",
							wifiSn, userId, netBindDeviceVo.getSystemId(), netBindDeviceVo.getLon(),
							netBindDeviceVo.getLat()), e);
					throw new CustomException(ExceptionEnum.DEVICE_BIND_FAILURE);
				} finally {
					lock.unlock();
				}
			}else {
				throw new CustomException(ExceptionEnum.DEVICE_BIND_ING);
			}
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
	}

    @Override
    public void deviceShareInstaller(String code) {
		// 校验二维码合法
		String codeStr = checkQrCode(code);
		// 校验二维码是否过期
		String paramStr = checkQrTime(codeStr, 30);
		// 解析二维码数据
		Long deviceId;
		int saveDeviceTime = 3;
		if (paramStr.contains(Constants.COMMA)) {
			String[] paramArr = paramStr.split(Constants.COMMA);
			deviceId = Long.parseLong(paramArr[0]);
			saveDeviceTime = Integer.parseInt(paramArr[1]);
		} else {
			deviceId = Long.parseLong(paramStr);
		}
		// 校验同数据中心
		DeviceListDO deviceInfo = deviceListService.getDeviceById(deviceId);
		ActionFlagUtil.assertTrue(deviceInfo != null);
		// 校验是否已绑定过
		checkAlreadyBound(deviceId);
		// 执行绑定逻辑
		if (DeviceSaveTimeEnum.ALWAYS.getCode() == saveDeviceTime) {
			authCenterResClient.bindInstaller(BindReqDTO.builder()
					.userId(UserInfoUtil.currentUserId())
					.roleId(RoleConstants.ROLE_INSTALLER)
					.resourceId(String.valueOf(deviceId))
					.build());
		} else {
			authCenterResClient.bindInstaller(BindReqDTO.builder()
					.userId(UserInfoUtil.currentUserId())
					.roleId(RoleConstants.ROLE_INSTALLER)
					.resourceId(String.valueOf(deviceId))
					.bindTime(System.currentTimeMillis())
					.build());
		}
	}

	/**
	 * 是否已经绑定
	 *
	 * @param deviceId 设备Id
	 */
	private void checkAlreadyBound(Long deviceId) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(String.valueOf(deviceId), null, null,
				null, null, null, null, null, null, null,
				null, null, null, null, null);
		if (CollUtil.isNotEmpty(resourceList)) {
			throw new CustomException(ExceptionEnum.DEVICE_ALREADY_BIND);
		}
	}

	/**
	 * 校验二维码合法性
	 *
	 * @param code 二维码
	 * @return 解析结果
	 */
	private String checkQrCode(String code) {
		return RotUtil.decode(code.substring(1), Integer.parseInt(code.substring(0, 1)))
				.orElseThrow(() -> {
					log.warn("二维码解密失败");
					return new CustomException(ExceptionEnum.INVALID_ACTION_TO_DEVICE);
				});
	}

	/**
	 * 校验二维码是否过期
	 *
	 * @param codeStr 二维码解析结果
	 * @param expiredTime 过期时间
	 * @return 反序列化结果
	 */
	private String checkQrTime(String codeStr, int expiredTime) {
		try {
			String minute = codeStr.substring(0, 8);
			long currMinute = System.currentTimeMillis() / 1000 / 60;
			if ((currMinute - Long.parseLong(minute)) > expiredTime) {
				log.warn("超过转移时效");
				throw new CustomException(ExceptionEnum.INVALID_ACTION_TO_DEVICE);
			}
			return codeStr.substring(8);
		} catch (Exception e) {
			log.warn(e.getMessage());
			throw new CustomException(ExceptionEnum.INVALID_ACTION_TO_DEVICE);
		}
	}

    /**
	 * 绑定账号
	 *
	 * @param netBindDeviceVo 配网入参
	 * @param userId userId
	 * @return 绑定结果
	 */
	private <T extends NetBindDeviceVO> NetBindDeviceDTO netDeviceBind(T netBindDeviceVo, Long userId) {
		String wifiSn = netBindDeviceVo.getWifiSn();

		// 根据系统id获取当前系统信息
		SystemInfoDO sysInfo = systemInfoService.getById(netBindDeviceVo.getSystemId());
		ActionFlagUtil.assertTrue(sysInfo != null);
		// 设备加速
//		deviceSpeedup(netBindDeviceVo);
		// 根据WiFi SN查询设备SN
		String deviceSn = getDeviceSnFromIot(netBindDeviceVo);
		// 根据WiFi Sn查询设备IP
//		String ip = getDeviceIpFromIot(netBindDeviceVo);
		// 根据IP查询归属地国家
//		String installCountry = getDeviceCountryByIp(ip);
//		netBindDeviceVo.setInstallCountry(installCountry);
		// 执行绑定逻辑
		bindDeviceAction(deviceSn, netBindDeviceVo, userId, sysInfo);
		// 自研棒子特殊处理：事务提交后，异步执行更新设备状态
		if (netBindDeviceVo instanceof NetBindWhVO) {
			TransactionUtils.afterCommit(() -> threadPoolTaskExecutor.execute(() -> retryService.syncWhDeviceState(deviceSn)));
		}
		return NetBindDeviceDTO.builder()
				.wifiSn(wifiSn)
				.deviceSn(deviceSn)
				.build();
	}

	/**
	 * 根据IP获取设备归属地国家
	 *
	 * @param ip ip
	 * @return 国家
	 */
	private String getDeviceCountryByIp(String ip) {
		if (StrUtil.isBlank(ip)) {
			return null;
		}
		Map<String, String> ipMap = ecosCountryService.queryIpList(Collections.singletonList(ip));
		return ipMap.getOrDefault(ip, ip);
	}

	/**
	 * 获取tuya设备的IP
	 *
	 * @param netBindDeviceVo 绑定入参
     * @return ip
     * @param <T> 绑定入参
	 */
	private <T extends NetBindDeviceVO> String getDeviceIpFromIot(T netBindDeviceVo) {
		String wifiSn = netBindDeviceVo.getWifiSn();
		String beanName = netBindDeviceVo instanceof NetBindWhVO ? "specificServ3" : "specificServ1";
		SpecificServService specificServService = InitUtil.getBean(beanName, SpecificServService.class);
		String ip = specificServService.getDeviceIpByWifiSn(wifiSn);
		if (StrUtil.isBlank(ip)) {
			bindFailMetrics.recordFailure(wifiSn, "获取设备ip失败");
			log.error("get device ip is blank: {}", wifiSn);
		}
		return ip;
	}

	/**
	 * 获取tuya上deviceSn
	 *
	 * @param netBindDeviceVo 绑定设备入参
     * @return deviceSn
     * @param <T> 绑定设备入参
	 */
	private <T extends NetBindDeviceVO> String getDeviceSnFromIot(T netBindDeviceVo) {
		if (netBindDeviceVo instanceof NetBindWhVO) {
			return ((NetBindWhVO) netBindDeviceVo).getDeviceSn();
		}
		String wifiSn = netBindDeviceVo.getWifiSn();
		SpecificServService specificServService = InitUtil.getBean("specificServ1",
				SpecificServService.class);
		String deviceSn = specificServService.getDeviceSnByWifiSn(wifiSn);
		if (StrUtil.isBlank(deviceSn)) {
			bindFailMetrics.recordFailure(wifiSn, "读取设备sn失败");
			log.error("getDeviceSnByWifiSn is blank : {}", wifiSn);
			throw new EcosException(ExceptionEnum.DEVICE_BIND_FAILURE);
		}
		log.info("bind installer UserDevice deviceSn: {}", deviceSn);
		return deviceSn;
	}

	/**
	 * 是否已经被绑定过，如果被其他安装商绑定过，则清除原有安装商绑定关系
	 *
	 * @param deviceInfo deviceInfo
	 * @param userId userId
	 * @return 绑定关系
	 */
	private void checkBindExists(DeviceListDO deviceInfo, Long userId) {
		Long deviceId = deviceInfo.getId();
		log.info("wifi sn already bind with device: {}", deviceInfo.getDeviceSn());
		List<BindInfoDTO> bindInfoList = authCenterResClient.getBindInfo(String.valueOf(deviceId), RoleConstants.ROLE_INSTALLER);
		if (CollUtil.isEmpty(bindInfoList)) {
			return;
		}
		BindInfoDTO bindInfo = bindInfoList.get(0);
		log.info("device already bind with user: {}", JSONObject.toJSONString(bindInfo));
		if (!String.valueOf(userId).equals(bindInfo.getUserId())) {
			// 如果已被其他安装商绑定:1、解除已有安装商设备与系统关系 2、退回已有安装商的系统的操作步骤
			Long installerId = Long.parseLong(bindInfo.getUserId());
			Long curDeviceId = Long.parseLong(bindInfo.getResourceId());
			boolean flag = systemInfoService.backStepSystem(installerId, curDeviceId);
			ActionFlagUtil.assertTrue(flag);
			boolean res = systemDeviceRelService.removeRel(installerId, curDeviceId);
			ActionFlagUtil.assertTrue(res);
		}
	}

	/**
	 * 加速采样
	 *
	 * @param netBindDeviceVo wifi sn
	 */
	private <T extends NetBindDeviceVO> void deviceSpeedup(T netBindDeviceVo) {
		String wifiSn = netBindDeviceVo.getWifiSn();
		if (netBindDeviceVo instanceof NetBindWhVO) {
			threadPoolTaskExecutor.execute(() -> retryService.speedUpDevice(wifiSn, DeviceDatasourceEnum.WH.getDatasource()));
			return;
		}
		threadPoolTaskExecutor.execute(() -> retryService.speedUpDevice(wifiSn, DeviceDatasourceEnum.TUYA.getDatasource()));
	}

	/**
	 * 执行绑定过程，如果被其他安装商绑定过，则覆盖
	 *
	 * @param deviceSn deviceSn
	 * @param userId wifiSn
	 * @param systemInfo 系统信息
	 */
	private <T extends NetBindDeviceVO> void bindDeviceAction(String deviceSn, T bindParam, Long userId, SystemInfoDO systemInfo) {
		DeviceListDO deviceInfo = deviceListService.lambdaQuery().eq(DeviceListDO::getDeviceName, deviceSn).one();
		if (null == deviceInfo) {
			DeviceListDefaultDO defaultCenterDevice = deviceListDefaultService.getDeviceInfoByDeviceName(deviceSn);
			deviceInfo = new DeviceListDO();
			BeanUtils.copyProperties(defaultCenterDevice, deviceInfo);
			deviceListService.save(deviceInfo);
		}
		log.info("device: [{}] is exists, has been updated", deviceSn);
		Long deviceId = deviceInfo.getId();
		// 检查当前设备是否已经被绑定过
		checkBindExists(deviceInfo, userId);
		// 更新设备信息
		deviceInfo.setDatacenterId(systemInfo.getDatacenterId());
		bindParam.buildSourceParam();
		deviceListService.updateDeviceInfo(deviceInfo, bindParam);
		// 绑定中间表
		authCenterResClient.bindInstaller(BindReqDTO.builder()
				.userId(String.valueOf(userId))
				.resourceId(String.valueOf(deviceId))
				.build());
		// 添加系统设备
		boolean addFlag = systemDeviceRelService.addDevice(systemInfo.getId(), deviceId);
		ActionFlagUtil.assertTrue(addFlag);
		// 无论当前是第几步，添加设备绑定都应该更新为步骤2
		boolean stepFlag = systemInfoService.updateById(SystemInfoDO.builder()
				.id(systemInfo.getId())
				.currentStep(StepEnum.STEP_2.getStepNum())
				.build());
		ActionFlagUtil.assertTrue(stepFlag);
	}

	@Override
	public DeviceBoundBindAccountDto deviceBindAgent(DeviceBoundBindAccountVo deviceBoundBindAccountVo) {
		// 仅限于给vpp用户操作
		String roleId = UserInfoUtil.currentUserRoleId();
		// 封装结果
		List<String> deviceSnList = deviceBoundBindAccountVo.getDeviceSnList();
		List<String> notExistSnList = new ArrayList<>();
		List<String> invalidSnList = new ArrayList<>();
		// 过滤无效Sn和不存在的Sn
		Map<Long, String> idSnMap = filterAndQueryDeviceByDeviceSnList(deviceSnList, invalidSnList, notExistSnList);
		if (CollUtil.isEmpty(idSnMap)) {
			return DeviceBoundBindAccountDto.builder()
					.bindedList(Collections.emptyList())
					.notExistList(notExistSnList)
					.invalidList(invalidSnList)
					.build();
		}
		VppBindResDTO res = authCenterResClient.vppBind(VppBindReqDTO.builder()
				.userId(UserInfoUtil.currentUserId())
				.roleId(roleId)
				.resourceCodes(new ArrayList<>(idSnMap.values()))
				.build());
		if (CollUtil.isNotEmpty(res.getFail())) {
			invalidSnList.addAll(res.getFail());
		}
		if (CollUtil.isNotEmpty(res.getNoPermission())) {
			invalidSnList.addAll(res.getNoPermission());
		}
		return DeviceBoundBindAccountDto.builder()
				.bindedList(res.getBound())
				.notExistList(notExistSnList)
				.invalidList(invalidSnList)
				.build();
	}

	@Override
	public DeviceBoundUnBindAccountDto deviceUnBindAgent(DeviceBoundBindAccountVo deviceBoundBindAccountVo) {
		// 仅限于给vpp用户操作
		String roleId = UserInfoUtil.currentUserRoleId();
		// 封装结果
		DeviceBoundUnBindAccountDto res = new DeviceBoundUnBindAccountDto();
		List<String> deviceSnList = deviceBoundBindAccountVo.getDeviceSnList();
		if (CollUtil.isEmpty(deviceBoundBindAccountVo.getDeviceSnList())) {
			return new DeviceBoundUnBindAccountDto();
		}
		List<String> notExistSnList = new ArrayList<>();
		List<String> invalidSnList = new ArrayList<>();
		// 过滤无效Sn和不存在的Sn
		Map<Long, String> idSnMap = filterAndQueryDeviceByDeviceSnList(deviceSnList, invalidSnList, notExistSnList);
		if (CollUtil.isNotEmpty(idSnMap)) {
			// 批量解绑
			authCenterResClient.vppUnbind(VppBindReqDTO.builder()
					.userId(UserInfoUtil.currentUserId())
					.roleId(roleId)
					.resourceCodes(new ArrayList<>(idSnMap.values()))
					.build());
		}
		res.setInvalidList(invalidSnList);
		res.setNotExistList(notExistSnList);
		return res;
	}

	@Override
	public void onlyProcessBoundDevice(String deviceName) {
		DeviceListDO deviceListDO = deviceListService.getDeviceInfoByDeviceName(deviceName);
		if (null == deviceListDO) {
			throw new RoleException(ExceptionEnum.DEVICE_NOT_EXIST);
		}
		RoleInfo roleInfo = UserInfoUtil.currentUserRole();
		if (roleInfo != null && roleInfo.getAllResourceStat() == 2) {
			return;
		}
		List<BindInfoDTO> bindInfoList = authCenterResClient.getBindInfo(String.valueOf(deviceListDO.getId()),
				UserInfoUtil.currentUserRoleId());
		if (CollUtil.isEmpty(bindInfoList)) {
			throw new RoleException(ExceptionEnum.ROLE_NOT_ALLOWED);
		}
	}

	@Override
	public List<String> filterBoundDevice() {
		String userId = UserInfoUtil.currentUserId();
		String roleCategory = UserInfoUtil.currentUserRoleCategory();
		List<String> deviceNames = new ArrayList<>();
		if (!RoleConstants.NEED_FILTER_ROLE.contains(roleCategory)) {
			return deviceNames;
		}
		List<ResourceResDTO> bindInfoList = authCenterResClient.resourceList(null, UserInfoUtil.currentUserRoleId(),
				userId, null, null, null,
				null, null, null, null, null, null,
				null, datacenter, null);
		if (CollUtil.isEmpty(bindInfoList)) {
			return deviceNames;
		}
		return bindInfoList.stream()
				.map(ResourceResDTO::getCode)
				.collect(Collectors.toList());
	}

	@Override
	public void boundInstall(InstallBoundDTO item) {
		if (DeviceSaveTimeEnum.ALWAYS.getCode() == item.getSaveDeviceTime()) {
			authCenterResClient.bindInstaller(BindReqDTO.builder()
					.userId(String.valueOf(item.getUserId()))
					.roleId(RoleConstants.ROLE_INSTALLER)
					.roleCategory(RoleConstants.ROLE_INSTALLER)
					.resourceId(String.valueOf(item.getDeviceId()))
					.build());
		} else if (DeviceSaveTimeEnum.DAY_3.getCode() == item.getSaveDeviceTime()) {
			authCenterResClient.bindInstaller(BindReqDTO.builder()
					.userId(String.valueOf(item.getUserId()))
					.roleId(RoleConstants.ROLE_INSTALLER)
					.roleCategory(RoleConstants.ROLE_INSTALLER)
					.resourceId(String.valueOf(item.getDeviceId()))
					.bindTime(System.currentTimeMillis())
					.build());
		} else if (DeviceSaveTimeEnum.NEVER.getCode() == item.getSaveDeviceTime()) {
			authCenterResClient.unbindInstaller(BindReqDTO.builder()
					.userId(String.valueOf(item.getUserId()))
					.roleId(RoleConstants.ROLE_INSTALLER)
					.roleCategory(RoleConstants.ROLE_INSTALLER)
					.resourceId(String.valueOf(item.getDeviceId()))
					.build());
		}
	}

	@Override
	@DSTransactional
	public NetBindDeviceDTO iotNetDeviceBind(NetBindWhVO netBindDeviceVo) {
		Long userId = Long.parseLong(UserInfoUtil.currentUserId());
		String deviceSn = netBindDeviceVo.getDeviceSn();
		RLock lock = redissonClient.getLock(deviceSn);
		try {
			if (lock.tryLock(10, TimeUnit.SECONDS)) {
				try {
					return netDeviceBind(netBindDeviceVo, userId);
				} catch (Exception e) {
					bindFailMetrics.recordFailure(netBindDeviceVo.getWifiSn(), "iot设备绑定最终失败");
					log.error(String.format("wifiSn:%s, userId:%s, systemId:%s, lon:%s, lat:%s bind failure",
							deviceSn, userId, netBindDeviceVo.getSystemId(), netBindDeviceVo.getLon(),
							netBindDeviceVo.getLat()), e);
					throw new CustomException(ExceptionEnum.DEVICE_BIND_FAILURE);
				} finally {
					lock.unlock();
				}
			}else {
				throw new CustomException(ExceptionEnum.DEVICE_BIND_ING);
			}
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public Boolean iotIsOnline(String wifiSn) {
		return whIotClient.queryIsOnlineStatus(wifiSn);
	}

	@Override
	public InstallBoundInfoDTO getBindInfo(String deviceId, String roleId) {
		List<BindInfoDTO> bindInfoList = authCenterResClient.getBatchBindInfo(BindQueryReqDTO.builder()
				.resourceIds(Collections.singletonList(deviceId))
				.roleId(roleId)
				.build());
		if (CollUtil.isEmpty(bindInfoList)) {
			return new InstallBoundInfoDTO();
		}
		BindInfoDTO bindInfoDTO = bindInfoList.get(0);
		Long expiredTime = bindInfoDTO.getExpireAt() == 0? null : DateUtil.between(new Date(),
				new Date(bindInfoDTO.getExpireAt()), DateUnit.SECOND);
		return InstallBoundInfoDTO.builder()
				.installId(bindInfoDTO.getUserId())
				.installName(bindInfoDTO.getEmail())
				.bindTime(DateUtil.format(new Date(bindInfoDTO.getCt()), DatePattern.NORM_DATETIME_PATTERN))
				.countdownTime(expiredTime)
				.build();
	}

	@Override
	public AccountBindInfoDTO getDevicesByAccount(String account) {
		DataResponse<AccountBindInfoDTO> res = ecosClientApi.getDevicesByAccount(account);
		AccountBindInfoDTO accountBindInfo = DataResponse.parseData(res);
		if (accountBindInfo == null) {
			return new AccountBindInfoDTO();
		}
		List<String> masterDeviceList = accountBindInfo.getMasterDeviceList();
		List<String> subDeviceList = accountBindInfo.getSubDeviceList();
		List<String> homeDeviceList = accountBindInfo.getHomeDeviceList();
		List<String> idList = new ArrayList<>();
		if (masterDeviceList != null) {
			idList.addAll(masterDeviceList);
		}
		if (subDeviceList != null) {
			idList.addAll(subDeviceList);
		}
		if (homeDeviceList != null) {
			idList.addAll(homeDeviceList);
		}
		if (CollUtil.isEmpty(idList)) {
			return new AccountBindInfoDTO();
		}
		Collection<DeviceListDO> deviceList = deviceListService.listByIds(idList);
		Map<String, String> map = deviceList.stream()
				.collect(Collectors.toMap(i -> String.valueOf(i.getId()), DeviceListDO::getDeviceSn));
		if (masterDeviceList != null) {
			accountBindInfo.setMasterDeviceList(masterDeviceList.stream().map(map::get).collect(Collectors.toList()));
		}
		if (subDeviceList != null) {
			accountBindInfo.setSubDeviceList(subDeviceList.stream().map(map::get).collect(Collectors.toList()));
		}
		if (homeDeviceList != null) {
			accountBindInfo.setHomeDeviceList(homeDeviceList.stream().map(map::get).collect(Collectors.toList()));
		}
		return accountBindInfo;
	}

	@Override
	public void iotResetDevice(String wifiSn) {
		whIotClient.sendEsResetCommand(wifiSn);
	}

}
