package com.weihengtech.service.device.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.cglib.CglibUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.weihengtech.auth.dto.*;
import com.weihengtech.auth.model.dto.UserResDTO;
import com.weihengtech.auth.rpc.AuthCenterClient;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.*;
import com.weihengtech.dao.device.DeviceListMapper;
import com.weihengtech.dao.other.SqlMapper;
import com.weihengtech.enums.charger.ChargerTypeModelEnum;
import com.weihengtech.enums.device.DeviceStatusEnum;
import com.weihengtech.enums.device.ImportTypeEnum;
import com.weihengtech.enums.device.RatedPowerEnum;
import com.weihengtech.enums.device.ResourceTypeEnum;
import com.weihengtech.enums.device.SeriesEnum;
import com.weihengtech.enums.firmware.UploadTypeEnum;
import com.weihengtech.enums.other.DatacenterEnum;
import com.weihengtech.pojo.bos.device.DeviceListPageBo;
import com.weihengtech.pojo.dos.charger.ChargerInfoDO;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.*;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.vos.charger.NetBindChargerVO;
import com.weihengtech.pojo.vos.device.*;
import com.weihengtech.pojo.vos.tsdb.TsdbParseErrorVo;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.device.DeviceRelationService;
import com.weihengtech.service.ext.ExtCommonService;
import com.weihengtech.service.other.EcosCountryService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.service.parser.ParseErrorService;
import com.weihengtech.service.specific.SpecificServService;
import com.weihengtech.service.specific.StrategyService;
import com.weihengtech.service.systeminfo.SystemInfoService;
import com.weihengtech.service.thirdpart.TimeSeriesDatabaseService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.BeanUtil;
import com.weihengtech.utils.ModbusRequestUtil;
import com.weihengtech.utils.OperatingRecordUtil;
import com.weihengtech.utils.OperationUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import com.weihengtech.utils.SpecialParseUtil;
import com.weihengtech.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.weihengtech.consts.RoleConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceListServiceImpl extends ServiceImpl<DeviceListMapper, DeviceListDO> implements DeviceListService {

	/**
	 * 反向过滤（不包含）的搜索条件
	 */
	private static final String EXCLUDE_PARAM = "0";

	@Value("${custom.datacenter.name}")
	private String datacenter;

	@Resource
	private SqlMapper sqlMapper;
	@Resource
	private SnowFlakeUtil snowFlakeUtil;
	@Resource
	private StrategyService strategyService;
	@Resource
	private ModbusRequestUtil modbusRequestUtil;
	@Resource
	private EcosCountryService ecosCountryService;
	@Resource
	private TuyaDatacenterService tuyaDatacenterService;
	@Resource
	private AuthCenterClient authCenterClient;
	@Resource
	private AuthCenterResClient authCenterResClient;
	@Resource
	private SystemInfoService systemInfoService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private DeviceRelationService deviceRelationService;

	@Override
	public PageInfoDTO<DeviceListPageDTO> pageDeviceList(DeviceListPageBo param, String redisSubName) {
		// 通过资源平台查询当前账号绑定设备
		List<ResourceResDTO> resourceList = getResourceList(param);
		if (CollUtil.isEmpty(resourceList)) {
			return new PageInfoDTO<>();
		}
		PageInfoDTO<DeviceListPageDTO> pageInfoDTO = new PageInfoDTO<>();
		// 构建设备基础数据
		Map<String, ResourceResDTO> resourceMap = resourceList.stream()
				.collect(Collectors.toMap(ResourceResDTO::getId, Function.identity()));
		// 分页查询
		param.setDeviceIdList(resourceMap.keySet());
		PageHelper.startPage(param.getPageNum(), param.getPageSize());
		List<DeviceListPageDTO> resList = sqlMapper.queryDeviceInHomePage(param);
		// 构造分页信息
		PageInfo<DeviceListPageDTO> pageInfo = new PageInfo<>(resList);
		pageInfoDTO.setTotalPages(pageInfo.getPages());
		pageInfoDTO.setTotalCount(pageInfo.getTotal());
		// 从平台读取账号数据
		List<ResourceResDTO> needTransList = resList.stream()
				.map(i -> resourceMap.get(i.getId()))
				.collect(Collectors.toList());
		List<UserResDTO> userList = getAgentUserList(needTransList);
		Map<String, String> userNickNameMap = userList.stream()
				.filter(i -> StrUtil.isNotBlank(i.getNickName()))
				.collect(Collectors.toMap(UserResDTO::getId, UserResDTO::getNickName));
		Map<String, String> userEmailMap = userList.stream()
				.filter(i -> StrUtil.isNotBlank(i.getEmail()))
				.collect(Collectors.toMap(UserResDTO::getId, UserResDTO::getEmail));
		// 从平台读取系列数据
		Map<Integer, String> seriesMap = getSeriesCodeMap();
		resList.forEach(dto -> {
			ResourceResDTO resource = resourceMap.get(dto.getId());
			Map<String, String> userInRoleCategories = resource.getUserInRoleCategories();
			dto.setId(resource.getId());
			dto.setDeviceSn(resource.getCode());
			dto.setDeviceName(resource.getName());
			dto.setAlias(resource.getRemark());
			dto.setRemark(resource.getRemark());
			dto.setDesc(resource.getDesc());
			dto.setCountry(resource.getDeliverAreaName());
			dto.setResourceType(resource.getTypeName());
			dto.setResourceSeries(resource.getCategoryName());
			dto.setResourceSeriesCode(seriesMap.get(resource.getCategory()));
			// 填充销售信息
			Optional.ofNullable(userInRoleCategories.get(ROLE_SALE)).ifPresent(
					k -> {
						dto.setSaleName(userNickNameMap.getOrDefault(k, "--"));
						dto.setSaleEmail(userEmailMap.getOrDefault(k, "--"));
					}
			);

			// 填充经销商信息
			Optional.ofNullable(userInRoleCategories.get(ROLE_AGENT)).ifPresent(
					k -> {
						dto.setAgentName(userNickNameMap.getOrDefault(k, "--"));
						dto.setAgentEmail(userEmailMap.getOrDefault(k, "--"));
					}
			);

			// 填充分销商信息
			Optional.ofNullable(userInRoleCategories.get(ROLE_DEALER)).ifPresent(
					k -> {
						dto.setDealerName(userNickNameMap.getOrDefault(k, "--"));
						dto.setDealerEmail(userEmailMap.getOrDefault(k, "--"));
					}
			);

			// 填充零售商信息
			Optional.ofNullable(userInRoleCategories.get(ROLE_RETAILER)).ifPresent(
					k -> {
						dto.setRetailerName(userNickNameMap.getOrDefault(k, "--"));
						dto.setRetailerEmail(userEmailMap.getOrDefault(k, "--"));
					}
			);

			// 填充安装商信息
			Optional.ofNullable(userInRoleCategories.get(ROLE_INSTALLER)).ifPresent(
					k -> {
						dto.setInstallerName(userNickNameMap.getOrDefault(k, "--"));
						dto.setInstallerEmail(userEmailMap.getOrDefault(k, "--"));
					}
			);
			dto.setSetupTime((null == dto.getSetupTime() || 0 == dto.getSetupTime()) ? -1 : dto.getSetupTime());
			dto.setUpdateTime(TimeUtil.localDateTimeToSerialString(dto.getUpdTime()));
			if (resource.getExpireAt() != null && resource.getExpireAt() != 0) {
				long expiredTime = DateUtil.between(new Date(), new Date(resource.getExpireAt()), DateUnit.SECOND);
				dto.setCountDownTime(expiredTime);
				dto.setIsNeedCountdown(Boolean.TRUE);
			}
			if (2 != UserInfoUtil.currentUser().getSource()) {
				dto.buildDefaultVersion();
			}
		});
		pageInfoDTO.setData(resList);
		return pageInfoDTO;
	}

	/** 获取系列映射关系 */
	private Map<Integer, String> getSeriesCodeMap() {
		List<ResourceConfigDTO> seriesList = authCenterResClient.categoryList();
		return seriesList.stream()
				.collect(Collectors.toMap(ResourceConfigDTO::getId, ResourceConfigDTO::getCode));
	}

	/** 获取资源绑定账号信息 */
	private List<UserResDTO> getAgentUserList(List<ResourceResDTO> resourceList) {
		Set<String> userIdList = resourceList.stream()
				.map(ResourceResDTO::getUserInRoleCategories)
				.filter(CollUtil :: isNotEmpty)
				.map(i -> {
					Set<String> userSet = new HashSet<>();
					Optional.ofNullable(i.get(ROLE_SALE)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_AGENT)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_DEALER)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_RETAILER)).ifPresent(userSet::add);
					Optional.ofNullable(i.get(ROLE_INSTALLER)).ifPresent(userSet::add);
					return userSet;
				})
				.flatMap(Collection::parallelStream)
				.collect(Collectors.toSet());
		if (CollUtil.isEmpty(userIdList)) {
			return Collections.emptyList();
		}
		return authCenterClient.list(String.join(Constants.COMMA, userIdList), null);
	}

	/** 查询账号平台资源数据 */
	private List<ResourceResDTO> getResourceList(DeviceListPageBo param) {
		String curRoleId = UserInfoUtil.currentUserRoleCategory();
		Map<String, String> userInRoleCategories = new HashMap<>();
		Map<String, String> excludeUserInRoleCategories = new HashMap<>();
		buildRoleFilterParam(param, userInRoleCategories, excludeUserInRoleCategories);
		List<ResourceResDTO> list;
		try {
			com.weihengtech.auth.model.bo.PageInfo<ResourceResDTO> pageInfo = authCenterResClient.resourceSearch(
					ResourceSearchReqDTO.builder()
							.userInRoleCategories(userInRoleCategories)
							.excludeUserInRoleCategories(excludeUserInRoleCategories)
							.remark(param.getAlias())
							.desc(param.getDesc())
							.name(param.getDeviceName())
							.deliverArea(param.getCountryId())
							.dataCenterCode(datacenter)
							.type(param.getResourceType())
							.category(param.getResourceSeries())
							.types(param.getResourceTypeList())
							.build());
			list = pageInfo.getData() == null ? Collections.emptyList() : pageInfo.getData();
		} catch (Exception e) {
			log.error(String.format("getResourceList failed: %s", e.getMessage()), e);
			list = Collections.emptyList();
		}
		// 如果是安装商，需要对设备列表特殊过滤
		if (RoleConstants.ROLE_INSTALLER.equals(curRoleId)) {
			List<String> validDeviceList = systemInfoService.getValidDeviceList(UserInfoUtil.currentUserId(),
					list.stream()
							.map(ResourceResDTO::getId)
							.collect(Collectors.toList()));
			list = list.stream()
					.filter(i -> validDeviceList.contains(i.getId()))
					.collect(Collectors.toList());
		}
		return list;
	}

	private void buildRoleFilterParam(DeviceListPageBo param,
									  Map<String, String> userInRoleCategories,
									  Map<String, String> excludeUserInRoleCategories) {
		if (StrUtil.isNotBlank(param.getSaleId())) {
			if (EXCLUDE_PARAM.equals(param.getSaleId())) {
				excludeUserInRoleCategories.put(ROLE_SALE, StrUtil.EMPTY);
			} else {
				userInRoleCategories.put(ROLE_SALE, param.getSaleId());
			}
		}
		if (StrUtil.isNotBlank(param.getAgentId())) {
			if (EXCLUDE_PARAM.equals(param.getAgentId())) {
				excludeUserInRoleCategories.put(ROLE_AGENT, StrUtil.EMPTY);
			} else {
				userInRoleCategories.put(ROLE_AGENT, param.getAgentId());
			}
		}
		if (StrUtil.isNotBlank(param.getDealerId())) {
			if (EXCLUDE_PARAM.equals(param.getDealerId())) {
				excludeUserInRoleCategories.put(ROLE_DEALER, StrUtil.EMPTY);
			} else {
				userInRoleCategories.put(ROLE_DEALER, param.getDealerId());
			}
		}
		if (StrUtil.isNotBlank(param.getRetailerId())) {
			if (EXCLUDE_PARAM.equals(param.getRetailerId())) {
				excludeUserInRoleCategories.put(ROLE_RETAILER, StrUtil.EMPTY);
			} else {
				userInRoleCategories.put(ROLE_RETAILER, param.getSaleId());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public BatchNewDeviceDto batchNewDevice(BatchNewDeviceVo param) {
		// 校验发货地区
		Integer countryId = param.getCountryId();
		if (!ecosCountryService.exist(countryId)) {
			throw new CustomException(ExceptionEnum.PARAM_VALID_EXCEPTION);
		}
		// 校验SN
		List<String> deviceSnList = param.getDeviceSnList().stream()
				.map(String::trim)
				.collect(Collectors.toList());
		if (CollUtil.isEmpty(deviceSnList)) {
			return new BatchNewDeviceDto()
					.withExistedSnList(Collections.emptyList())
					.withInvalidSnList(Collections.emptyList());
		}

		List<String> existSnList = new ArrayList<>();
		List<String> invalidSnList = new ArrayList<>();
		// 过滤已存在的设备SN
		List<DeviceListDO> existsDeviceList = this.list(Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getDeviceSn, deviceSnList));
		if (CollUtil.isNotEmpty(existsDeviceList)) {
			existSnList = existsDeviceList.stream()
					.map(DeviceListDO::getDeviceSn)
					.collect(Collectors.toList());
		}
		List<String> finalExistSnList = existSnList;
		List<String> newSnList = deviceSnList.stream()
				.filter(i -> !finalExistSnList.contains(i))
				.distinct()
				.collect(Collectors.toList());
		if (CollUtil.isEmpty(newSnList)) {
			return new BatchNewDeviceDto().withExistedSnList(existSnList).withInvalidSnList(invalidSnList);
		}
		// 过滤不合法的设备SN
		invalidSnList = newSnList.stream()
				.map(i -> SpecialParseUtil.parseDeviceSn(ImportTypeEnum.storage.getCode(), i))
				.filter(StrUtil::isBlank)
				.collect(Collectors.toList());
		List<String> finalInvalidSnList = invalidSnList;
		// 过滤需要新增的设备
		List<DeviceListDO> deviceList = newSnList.stream()
				.filter(i -> !finalInvalidSnList.contains(i))
				.map(i -> {
					DeviceListDO item = new DeviceListDO();
					item.initImportNewDevice(param.getType(), snowFlakeUtil.generateId(), i, countryId);
					return item;
				}).collect(Collectors.toList());
		if (CollUtil.isEmpty(deviceList)) {
			return new BatchNewDeviceDto()
					.withExistedSnList(existSnList)
					.withInvalidSnList(invalidSnList);
		}
		// 执行批量保存
		boolean res = this.saveBatch(deviceList);
		Assert.isTrue(res, "save batch failed");
		authCenterResClient.createResourceBatch(deviceList.stream()
				.map(i -> ResourceAddDTO.builder()
						.id(String.valueOf(i.getId()))
						.code(i.getDeviceSn())
						.name(i.getDeviceName())
						// 如果是充电桩则默认指定类型为未知充电桩
						.type(ImportTypeEnum.storage.getCode().equals(param.getType()) ? ResourceTypeEnum.getIdByPreSn(i.getDeviceSn()) : 15)
						.source(2)
						.sourceType(1)
						.deliverArea(countryId)
						.build())
				.collect(Collectors.toList()));
		return new BatchNewDeviceDto().withExistedSnList(existSnList).withInvalidSnList(invalidSnList);
	}

	@Override
	@DSTransactional
	public Pair<DeviceSyncVersionDto, Boolean> syncDeviceVersionByReadResult(
			DeviceInformationDTO deviceInformationDTO, DeviceListDO deviceListDO
	) {
		List<Boolean> matchList = ListUtil.toList(true);
		String deviceSn = SpecialParseUtil.parseDeviceSn(ImportTypeEnum.storage.getCode(), deviceInformationDTO.getDeviceSn());
		String bmsSn = SpecialParseUtil.parseDeviceSn(ImportTypeEnum.storage.getCode(), deviceInformationDTO.getBmsSn());
		matchList.add(StrUtil.isNotBlank(deviceSn));
		matchList.add(StrUtil.isNotBlank(bmsSn));
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.parseDeviceSn(ImportTypeEnum.storage.getCode(), deviceSn), "setDeviceSn");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.parseBmsSn(bmsSn), "setBmsSn");
		if (ResourceTypeEnum.isAgaveSh(deviceListDO.getDeviceSn())) {
			com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_MODEL, deviceInformationDTO.getModelName(), matchList, "DeviceModel"), "setDeviceModel");
		}
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_BRAND, deviceInformationDTO.getBrand(), matchList, "Brand"), "setBrand");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_FACTORY, deviceInformationDTO.getFactory(), matchList, "Factory"), "setFactory");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getPowerBoardHardwareVersion(), matchList, "PowerBoardHardwareVersion"), "setPowerBoardHardwareVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getDsp1Version(), matchList, "Dsp1SoftwareVersion"), "setDsp1SoftwareVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getDsp2Version(), matchList, "Dsp2SoftwareVersion"), "setDsp2SoftwareVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getEmsSoftwareVersion(), matchList, "EmsSoftwareVersion"), "setEmsSoftwareVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getEmsHardwareVersion(), matchList, "EmsHardwareVersion"), "setEmsHardwareVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getGaugeVersion(), matchList, "BmsGaugeVersion"), "setBmsGaugeVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_BMS_MANUFACTURER, deviceInformationDTO.getBmsManufacturer(), matchList, "BmsVendor"), "setBmsVendor");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getBmsSoftwareVersion(), matchList, "BmsSoftwareVersion"), "setBmsSoftwareVersion");
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getBmsHardwareVersion(), matchList, "BmsHardwareVersion"), "setBmsHardwareVersion");
		OperationUtil.of(deviceInformationDTO.getDsp1SubVersion()).then(deviceListDO::setDsp1SubVersion);
		OperationUtil.of(deviceInformationDTO.getDsp2SubVersion()).then(deviceListDO::setDsp2SubVersion);
		OperationUtil.of(deviceInformationDTO.getEmsSubVersion()).then(deviceListDO::setEmsSubVersion);
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getArcDspSoftwareVersion(), matchList, "ArcDspSoftwareVersion"), "setArcDspSoftwareVersion");
		OperationUtil.of(deviceInformationDTO.getArcDspSubVersion()).then(deviceListDO::setArcDspSubVersion);
		com.weihengtech.utils.BeanUtil.compareAndSetStr(deviceListDO, SpecialParseUtil.matchTransform(
				SpecialParseUtil.REGEX_352, deviceInformationDTO.getArcDspBootLoaderSoftwareVersion(), matchList, "ArcDspBootLoaderSoftwareVersion"), "setArcDspBootLoaderSoftwareVersion");
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		Map<String, String> firmwareVersionByTypeList = specificServService.getFirmwareVersionByTypeList(
				deviceListDO.getDeviceName(), UploadTypeEnum.WIFI.name());
		OperationUtil.of(firmwareVersionByTypeList.get("wifiSoftwareVersion"))
				.then(deviceListDO::setWifiSoftwareVersion);
		OperationUtil.of(firmwareVersionByTypeList.get("wifiHardwareVersion"))
				.then(deviceListDO::setWifiHardwareVersion);
		OperationUtil.of(firmwareVersionByTypeList.get("wifiSerial")).then(deviceListDO::setWifiSn);
		if (StrUtil.isNotBlank(deviceInformationDTO.getParallelNumber())) {
			deviceListDO.setParallelNumber(Integer.parseInt(deviceInformationDTO.getParallelNumber()));
		} else {
			deviceListDO.setParallelNumber(0);
		}

		// 安规ID
		deviceListDO.setSafetyStandard(Optional.ofNullable(deviceInformationDTO.getSafetyID()).map(Integer::parseInt).orElse(0));
		ActionFlagUtil.assertTrue(updateById(deviceListDO));
		DeviceSyncVersionDto deviceSyncVersionDto = new DeviceSyncVersionDto();
		CglibUtil.copy(deviceListDO, deviceSyncVersionDto);
		return Pair.of(deviceSyncVersionDto, matchList.stream().anyMatch(Boolean.FALSE::equals));
	}

	@Override
	public void handleDeviceState(DeviceStateHandleVO deviceStateHandleVO) {
		String action = deviceStateHandleVO.getAction();
		Integer actionInteger = Integer.parseInt(action);
		List<String> deviceNameList = deviceStateHandleVO.getDeviceNameList();
		List<DeviceListDO> deviceList = this.list(
				Wrappers.<DeviceListDO>lambdaQuery().in(DeviceListDO::getDeviceName, deviceNameList));
		for (DeviceListDO device : deviceList) {
			if (StrUtil.isNotBlank(device.getWifiSn())) {
				if (CommonConstants.DEVICE_STATE_ON.equals(actionInteger)) {
					modbusRequestUtil.postTransparentWrite(device.getDeviceName(), 42001, 1, 1, ListUtil.toLinkedList(0));
				} else if (CommonConstants.DEVICE_STATE_OFF.equals(actionInteger)) {
					modbusRequestUtil.postTransparentWrite(device.getDeviceName(), 42001, 1, 1, ListUtil.toLinkedList(1));
				} else if (CommonConstants.DEVICE_STATE_RESTART.equals(actionInteger)) {
					modbusRequestUtil.postTransparentWrite(device.getDeviceName(), 42002, 1, 1, ListUtil.toLinkedList(1));
				}
			}
		}
	}

	@Override
	public List<String> likeDeviceName(String deviceName) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(null, null, null, null, null,
				null, null, null, null, null, deviceName, null,
				null, datacenter, null);
		if (CollUtil.isEmpty(resourceList)) {
			return Collections.emptyList();
		}
		return resourceList.stream()
				.map(ResourceResDTO::getName)
				.collect(Collectors.toList());
	}

	@Override
	public List<String> likeWifiSn(String wifiSn) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(null, null, null, null, null,
				null, null, null, null, null, null, null,
				null, datacenter, null);
		if (CollUtil.isEmpty(resourceList)) {
			return Collections.emptyList();
		}
		List<Long> idList = resourceList.stream()
				.map(ResourceResDTO::getId)
				.map(Long :: parseLong)
				.collect(Collectors.toList());
		return sqlMapper.queryDeviceByLikeWifiSn(wifiSn, idList);
	}

	@Override
	public List<String> likeAlias(String alias) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList( null, null, null, null,
				null, null, null, alias, null, null, null, null,
				null, datacenter, null);
		if (CollUtil.isEmpty(resourceList)) {
			return Collections.emptyList();
		}
		return resourceList.stream()
				.map(ResourceResDTO::getRemark)
				.collect(Collectors.toList());
	}

	@Override
	public List<String> likeDesc(String desc) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList( null, null, null, null,
				null, null, null, null, desc, null, null, null,
				null, datacenter, null);
		if (CollUtil.isEmpty(resourceList)) {
			return Collections.emptyList();
		}
		return resourceList.stream()
				.map(ResourceResDTO::getDesc)
				.collect(Collectors.toList());
	}

	@Override
	public ParseErrorDTO parseError(TsdbParseErrorVo param) {
		ParseErrorService parseErrorService = strategyService.chooseParseErrServ(param.getDeviceSn());
		return parseErrorService.parseError(param);
	}

	@Override
	public String getAgentId(String deviceId) {
		List<BindInfoDTO> batchBindInfo = authCenterResClient.getBatchBindInfo(BindQueryReqDTO.builder()
				.resourceIds(Collections.singletonList(deviceId))
				.roleCategory(ROLE_AGENT)
				.build());
		if (CollUtil.isEmpty(batchBindInfo)) {
			return StrUtil.EMPTY;
		}
		return batchBindInfo.get(0).getUserId();
	}

	@Override
	public void updateDeviceCountry(Integer countryId, List<String> deviceSnList) {
		LambdaUpdateWrapper<DeviceListDO> updateWrapper = Wrappers.<DeviceListDO>lambdaUpdate()
				.in(DeviceListDO::getDeviceName, deviceSnList)
				.set(DeviceListDO::getCountryId, countryId);
		update(updateWrapper);
	}

	@Override
	public DeviceListDO getDeviceBeanByWifiSn(String wifiSn) {
		return this.getOne(Wrappers.<DeviceListDO>lambdaQuery().eq(DeviceListDO::getWifiSn, wifiSn));
	}

	@Override
	public List<String> listAuDeviceSn() {
		List<DeviceListDO> deviceList = getDeviceByDatacenter(DatacenterEnum.AU.name());
		return deviceList.stream()
				.map(DeviceListDO::getDeviceSn)
				.collect(Collectors.toList());
	}

    @Override
    public List<DeviceListDO> getNowBindDeviceList(String wifiSn) {
        return this.lambdaQuery().eq(DeviceListDO::getWifiSn, wifiSn).list();
    }

	@Override
	public List<DeviceListDO> getOtherBindDeviceList(String wifiSn, String deviceName) {
		return this.lambdaQuery().eq(DeviceListDO::getWifiSn, wifiSn).ne(DeviceListDO::getDeviceName, deviceName).list();
	}

    @Override
    public DeviceListDO querySameDatacenterDevice(Long deviceId) {
		DeviceListDO deviceListDO = getById(deviceId);
		BeanUtil.assertNotNull(deviceListDO);
		return deviceListDO;
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
    public Long saveDeviceInfo(String deviceSn, NetBindDeviceVO bindParam, int datacenterId) {
		// 保存设备列表主表
		Long deviceId = bindParam.getDeviceId() == null ? snowFlakeUtil.generateId() : bindParam.getDeviceId();
		DeviceListDO deviceInfo = DeviceListDO.buildSaveParam(deviceId, deviceSn, datacenterId, bindParam);
		save(deviceInfo);
		// 保存设备自定义字段
		saveOrUpdExt(deviceId, bindParam);
		// 同步平台资源信息
		authCenterResClient.createResourceBatch(Collections.singletonList(ResourceAddDTO.builder()
				.id(String.valueOf(deviceId))
				.code(deviceSn)
				.name(deviceSn)
				.type(Optional.ofNullable(bindParam.getResourceType())
						.orElse(ResourceTypeEnum.getIdByPreSn(deviceSn)))
				// 1-mes;2-hub;3-pangu
				.source(2)
				// 1-户储 2-大储
				.sourceType(1)
				.dataCenter(datacenterId)
				.build()));
		return deviceId;
    }

	/**
	 * 保存扩展属性
	 *
	 * @param deviceId 设备id
	 * @param bindParam 绑定参数
	 */
	private void saveOrUpdExt(Long deviceId, NetBindDeviceVO bindParam) {
		if (bindParam instanceof NetBindChargerVO) {
			NetBindChargerVO chargerParam = (NetBindChargerVO) bindParam;
			// 有扩展字段可保存
			ChargerInfoDO info = ChargerInfoDO.builder()
					.deviceId(deviceId)
					.mode(chargerParam.getBindMode())
					.maxPower(((NetBindChargerVO) bindParam).getMaxPower())
					.cpFirmwareVersion(chargerParam.getCpFirmwareVersion())
					.cpPlugAndChargeMsg(chargerParam.getCpPlugAndChargeMsg())
					.cpMode(chargerParam.getCpMode())
					.cpMeterType(chargerParam.getCpMeterType())
					.cpMeterRatio(chargerParam.getCpMeterRatio())
					.cpHomeCurrent(chargerParam.getCpHomeCurrent())
					.cpPvCurrent(chargerParam.getCpPvCurrent())
					.build();
			if (((NetBindChargerVO) bindParam).getMaxPower() == null) {
				info.setMaxPower(ChargerTypeModelEnum.getRatedPowerByModel(bindParam.getDeviceModel()));
			}
			ExtCommonService<ChargerInfoDO> extCommonService = strategyService.chooseExtServ(SeriesEnum.AC_Charger.getId());
			extCommonService.updExtByDeviceId(info);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateDeviceInfo(DeviceListDO deviceInfo, NetBindDeviceVO bindParam) {
		// 更新当前设备主表信息
		DeviceListDO.buildUpdParam(deviceInfo, bindParam);
		updateById(deviceInfo);
		List<DeviceListDO> beforeWifiBindDevice = getOtherBindDeviceList(bindParam.getWifiSn(), deviceInfo.getDeviceSn());
		if (CollUtil.isNotEmpty(beforeWifiBindDevice)) {
			beforeWifiBindDevice.forEach(deviceDo -> {
				deviceDo.setWifiSn("");
				deviceDo.setUpdateTime(LocalDateTime.now());
				updateById(deviceDo);
			});
		}
		// 更新扩展属性
		saveOrUpdExt(deviceInfo.getId(), bindParam);
		// 同步资源信息到平台
		authCenterResClient.updResource(ResourceUpdDTO.builder()
				.resourceId(String.valueOf(deviceInfo.getId()))
				.dataCenter(deviceInfo.getDatacenterId())
				.type(String.valueOf(bindParam.getResourceType()))
				.build());
	}

	@Override
	public List<String> getBatchIdsByWifiSn(Collection<String> wifiSnList) {
		List<DeviceListDO> list = this.list(Wrappers.<DeviceListDO>lambdaQuery()
				.in(DeviceListDO::getWifiSn, wifiSnList));
		if (CollUtil.isEmpty(list)) {
			return Collections.emptyList();
		}
		return list.stream()
				.map(DeviceListDO::getId)
				.map(String :: valueOf)
				.collect(Collectors.toList());
	}

	@Override
	public DeviceListDO getDeviceById(Long id) {
		// 查询资源信息
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
				.ids(String.valueOf(id))
				.dataCenter(datacenter)
				.build());
		if (CollUtil.isEmpty(resourceList)) {
			return null;
		}
		ResourceResDTO resource = resourceList.get(0);
		DeviceListDO deviceInfo = getById(id);
		// 计算vppMode真实值，弥补vpp下发定时开启指令结束后自动关闭的问题
		calVppMode(Collections.singletonList(deviceInfo));
		// 查询扩展信息
		ExtCommonService<Object> extCommonService = strategyService.chooseExtServ(resource.getCategory());
		Object extInfo = Optional.ofNullable(extCommonService)
				.map(i -> i.getExtInfoByDeviceId(id))
				.orElse(null);
        // 组合信息
		return Optional.of(deviceInfo)
				.map(i -> {
					i.setDatacenterId(resource.getDataCenter());
					i.setAlias(resource.getRemark());
					i.setCountryId(resource.getDeliverArea());
					i.setResourceTypeId(resource.getType());
					i.setResourceSeriesId(resource.getCategory());
					i.setExtInfo(extInfo);
					return i;
				}).orElse(null);
	}

	@Override
	public List<DeviceListDO> getDeviceByIds(Collection<String> idList, Boolean isNeedExt) {
		if (CollUtil.isEmpty(idList)) {
			return Collections.emptyList();
		}
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
				.ids(String.join(Constants.COMMA, idList))
				.dataCenter(datacenter)
				.build());
		List<DeviceListDO> deviceList = mergeDeviceList(resourceList, isNeedExt);
		// 计算vppMode真实值，弥补vpp下发定时开启指令结束后自动关闭的问题
		calVppMode(deviceList);
		return deviceList;
	}

	/** 计算vppMode真实值，弥补vpp下发定时开启指令结束后自动关闭的问题 */
	private void calVppMode(Collection<DeviceListDO> deviceList) {
		try {
			for (DeviceListDO deviceInfo : deviceList) {
				String countdownTime = stringRedisTemplate.opsForValue().get(RedisRefConstants.buildVppModeKey(deviceInfo.getDeviceSn()));
				if (StrUtil.isBlank(countdownTime) || Long.parseLong(countdownTime) > DateUtil.currentSeconds()) {
					continue;
				}
				deviceInfo.setVppMode(Boolean.FALSE);
				stringRedisTemplate.delete(RedisRefConstants.buildVppModeKey(deviceInfo.getDeviceSn()));
			}
		} catch (Exception e) {
			log.error("calVppMode failed: {}", e.getMessage());
		}
	}

	@Override
	public List<DeviceListDO> getDeviceByDatacenter(String datacenter) {
		List<ResourceResDTO> resourceList = authCenterResClient.resourceList(ResourceListReqDTO.builder()
						.dataCenter(datacenter)
				.build());
		return mergeDeviceList(resourceList, false);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveNewDevice(DeviceListDO deviceInfo) {
		// 初始化额定功率
		deviceInfo.setRatedPower(RatedPowerEnum.getPowerBySn(deviceInfo.getDeviceSn()));
		save(deviceInfo);
		// 同步平台资源信息
		authCenterResClient.createResourceBatch(
				Collections.singletonList(ResourceAddDTO.builder()
						.id(String.valueOf(deviceInfo.getId()))
						.code(deviceInfo.getDeviceSn())
						.name(deviceInfo.getDeviceName())
						.type(ResourceTypeEnum.getIdByPreSn(deviceInfo.getDeviceSn()))
						// 1-mes;2-hub;3-pangu
						.source(2)
						// 1-户储 2-大储
						.sourceType(1)
						.dataCenter(deviceInfo.getDatacenterId())
						.build()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updDevice(DeviceListDO deviceInfo) {
		log.info("client updDevice, param is: {}", JSONUtil.toJsonStr(deviceInfo));
		String ip = deviceInfo.getIp();
		if (StrUtil.isNotBlank(ip)) {
			Map<String, String> ipMap = ecosCountryService.queryIpList(Collections.singletonList(ip));
			deviceInfo.setIp(ipMap.getOrDefault(ip, ip));
		}
		updateById(deviceInfo);
		// 同步资源信息到平台
		if (deviceInfo.getDatacenterId() != null) {
			authCenterResClient.updResource(ResourceUpdDTO.builder()
					.resourceId(String.valueOf(deviceInfo.getId()))
					.dataCenter(deviceInfo.getDatacenterId())
					.build());
		}
	}

	@Override
	public List<DeviceLikeSnDTO> likeDeviceSn(String deviceName) {
		com.weihengtech.auth.model.bo.PageInfo<ResourceResDTO> pageInfo = authCenterResClient.resourceSearch(
				ResourceSearchReqDTO.builder()
						.name(deviceName)
						.dataCenterCode(datacenter)
						.build());
		List<ResourceResDTO> list = pageInfo.getData() == null ? Collections.emptyList() : pageInfo.getData();
		Map<String, DeviceListDO> map;
		if (CollUtil.isNotEmpty(list)) {
			List<String> idList = list.stream().map(ResourceResDTO::getId)
					.collect(Collectors.toList());
			Collection<DeviceListDO> deviceList = listByIds(idList);
			if (CollUtil.isNotEmpty(deviceList)) {
				// 计算vppMode真实值，弥补vpp下发定时开启指令结束后自动关闭的问题
				calVppMode(deviceList);
				map = deviceList.stream()
						.collect(Collectors.toMap(i -> String.valueOf(i.getId()), i -> i));
			} else {
                map = Collections.emptyMap();
            }
        } else {
            map = Collections.emptyMap();
        }
        // 从平台读取系列数据
		Map<Integer, String> seriesMap = getSeriesCodeMap();
		return list.stream()
				.map(i -> DeviceLikeSnDTO.builder()
						.id(i.getId())
						.deviceSn(i.getCode())
						.resourceSeriesCode(seriesMap.getOrDefault(i.getCategory(), StrUtil.EMPTY))
						.dsp1SoftwareVersion(map.containsKey(i.getId()) ? map.get(i.getId()).getDsp1SoftwareVersion() : StrUtil.EMPTY)
						.emsSoftwareVersion(map.containsKey(i.getId()) ? map.get(i.getId()).getEmsSoftwareVersion() : StrUtil.EMPTY)
						.vppMode(map.containsKey(i.getId()) ? map.get(i.getId()).getVppMode() : Boolean.FALSE)
						.parallelNumber(map.containsKey(i.getId()) ? map.get(i.getId()).getParallelNumber() : 0)
						.safetyStandard(map.containsKey(i.getId()) ? map.get(i.getId()).getSafetyStandard() : null)
						.deviceModel(map.containsKey(i.getId()) ? map.get(i.getId()).getDeviceModel() : null)
						.build())
				.collect(Collectors.toList());
	}

	@Override
	public PageInfoDTO<DeviceListPageDTO> storageList(DeviceListPageBo queryParam, String storageDeviceList) {
		List<String> storageTypeList = typeList();
		if (CollUtil.isEmpty(queryParam.getResourceTypeList())) {
			queryParam.setResourceTypeList(storageTypeList);
		} else {
			queryParam.getResourceTypeList().retainAll(storageTypeList);
			if (CollUtil.isEmpty(queryParam.getResourceTypeList())) {
				// 如果只查询非储能机类型，则返回空
				return new PageInfoDTO<>(0, 0L, Collections.emptyList());
			}
		}
		return pageDeviceList(queryParam, storageDeviceList);
	}

	@Override
	public PageInfoDTO<DeviceLikeSnDTO> snLikePage(DeviceLikePageVo pageVo) {
		// 储能机设备分页
		com.weihengtech.auth.model.bo.PageInfo<ResourceResDTO> pageInfo = authCenterResClient.resourceSearch(
				ResourceSearchReqDTO.builder()
						.code(pageVo.getDeviceSn())
						.dataCenterCode(datacenter)
						.types(typeList())
						.offset((pageVo.getPageNum()-1) * pageVo.getPageSize())
						.limit(pageVo.getPageSize())
						.build());
		List<ResourceResDTO> dataList = pageInfo.getData();
		List<DeviceLikeSnDTO> resList = Collections.emptyList();
		if (CollUtil.isNotEmpty(dataList)) {
			resList = dataList.stream()
					.map(i -> DeviceLikeSnDTO.builder()
							.id(i.getId())
							.deviceSn(i.getCode())
							.resourceSeriesCode(String.valueOf(i.getCategory()))
							.build())
					.collect(Collectors.toList());
		}
		return new PageInfoDTO(pageInfo.getOffset() / pageVo.getPageSize() + 1, pageInfo.getCount().longValue(), resList);
	}

	@Override
	public DeviceStatisticsDTO statistics() {
		List<ResourceResDTO> resourceList = getResourceList(DeviceListPageBo.builder().build());
		if (CollUtil.isEmpty(resourceList)) {
			return DeviceStatisticsDTO.builder()
					.totalSum(0)
					.installedSum(0)
					.connectedSum(0)
					.offlineSum(0)
					.faultSum(0)
					.lowBatterySum(0)
					.build();
		}
		List<Long> idList = resourceList.stream().map(ResourceResDTO::getId)
				.map(Long::parseLong).collect(Collectors.toList());
		List<DeviceStatusDTO> statusList = sqlMapper.queryDeviceStatus(idList);
		return DeviceStatisticsDTO.builder()
				.totalSum(statusList.size())
				.installedSum((int) statusList.stream().filter(i -> i.getSetUpTime() > 0).count())
				.connectedSum((int) statusList.stream().filter(i -> i.getState() >= 0).count())
				.offlineSum((int) statusList.stream().filter(i -> i.getSetUpTime() != 0 && i.getState() < 0).count())
				.faultSum((int) statusList.stream().filter(i -> i.getState() == 3).count())
				.lowBatterySum((int) statusList.stream().filter(i ->
						// 还要排除离线的设备
						i.getBatSoc() != null && i.getBatSoc() < 0.1 && i.getState() >= 0
						// 还要排除bms软件版本号为空
						&& StrUtil.isNotBlank(i.getBmsSoftwareVersion())
				).count())
				.build();
	}

	@Override
	public void updateDeviceAddress(DeviceAddressUpdateVo deviceAddressUpdateVo) {
		DeviceListDO deviceInfo = getDeviceById(Long.parseLong(deviceAddressUpdateVo.getId()));
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceInfo);

		String deviceName = deviceInfo.getDeviceName();
		String beforeAddress = deviceInfo.getAddress();
		String afterAddress = deviceAddressUpdateVo.getAddress();

		deviceInfo.setAddress(afterAddress);
		ActionFlagUtil.assertTrue(updateById(deviceInfo));

		OperatingRecordUtil.log(
				RecordContentConstants.UPDATE_DEVICE_ADDRESS,
				MapUtil.<String, String>builder()
						.put("deviceName", cn.hutool.core.util.StrUtil.nullToEmpty(deviceName))
						.put("beforeAddress", cn.hutool.core.util.StrUtil.nullToEmpty(beforeAddress))
						.put("afterAddress", cn.hutool.core.util.StrUtil.nullToEmpty(afterAddress))
						.build(),
				EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
		);
	}

	/** 获取储能机类型列表 */
	private List<String> typeList() {
		List<ResourceConfigDTO> seriesList = authCenterResClient.categoryTypeTree();
		if (CollUtil.isEmpty(seriesList)) {
			return Collections.emptyList();
		}
		return seriesList.stream()
				.filter(i -> SeriesEnum.Single_phase.getId().equals(i.getId()) ||
						SeriesEnum.Three_phase.getId().equals(i.getId()) ||
						SeriesEnum.NA_Device.getId().equals(i.getId()))
				.map(ResourceConfigDTO::getTypes)
				.flatMap(Collection::stream)
				.map(ResourceConfigDTO::getId)
				.map(String :: valueOf)
				.collect(Collectors.toList());
	}

	/**
	 * 合并资源数据与设备数据
	 *
	 * @param resourceList 资源列表
	 * @return 设备数据
	 */
	private List<DeviceListDO> mergeDeviceList(List<ResourceResDTO> resourceList, Boolean isNeedExt) {
		if (CollUtil.isEmpty(resourceList)) {
			return Collections.emptyList();
		}
		List<String> ids = resourceList.stream()
				.map(ResourceResDTO::getId)
				.collect(Collectors.toList());
		Map<String, ResourceResDTO> map = resourceList.stream()
				.collect(Collectors.toMap(ResourceResDTO::getId, Function.identity()));
		Collection<DeviceListDO> list = this.listByIds(ids);
		Map<Long, Object> extInfoMap = new HashMap<>();
		Map<Long, Boolean> mainMap = new HashMap<>();
		if (isNeedExt != null && isNeedExt) {
			extInfoMap = queryExtInfoBatch(resourceList);
			mainMap = deviceRelationService.getRelationInfo(ids.stream()
					.map(Long::parseLong)
					.collect(Collectors.toList()));
		}
		final Map<Long, Object> resMap = extInfoMap;
		final Map<Long, Boolean> masterMap = mainMap;
		return list.stream()
				.peek(i -> {
					ResourceResDTO resource = map.get(String.valueOf(i.getId()));
					i.setDatacenterId(resource.getDataCenter());
					i.setAlias(resource.getRemark());
					i.setCountryId(resource.getDeliverArea());
					i.setResourceSeriesId(resource.getCategory());
					i.setResourceTypeId(resource.getType());
					i.setExtInfo(resMap.get(i.getId()));
					i.setIsMain(masterMap.get(i.getId()));
				}).collect(Collectors.toList());
	}

	/** 批量查询扩展信息 */
	private Map<Long, Object> queryExtInfoBatch(List<ResourceResDTO> resourceList) {
		Map<Long, Object> resMap = new HashMap<>();
		if (CollUtil.isEmpty(resourceList)) {
			return resMap;
		}
		Map<Integer, List<ResourceResDTO>> categoryMap = resourceList.stream()
				.collect(Collectors.groupingBy(ResourceResDTO::getCategory));
		for (Map.Entry<Integer, List<ResourceResDTO>> entry : categoryMap.entrySet()) {
			Integer cateCode = entry.getKey();
			List<ResourceResDTO> resourceItemList = entry.getValue();
			// 查询扩展信息
			ExtCommonService<Object> extCommonService = strategyService.chooseExtServ(cateCode);
			Map<Long, Object> itemMap = Optional.ofNullable(extCommonService)
					.map(i -> i.getExtMapByDeviceIds(resourceItemList.stream()
							.map(ResourceResDTO::getId)
							.map(Long::parseLong)
							.collect(Collectors.toList())))
					.orElse(Collections.emptyMap());
			if (CollUtil.isNotEmpty(itemMap)) {
				resMap.putAll(itemMap);
			}
		}
		return resMap;
	}

	@Override
	public void updateVppMode(boolean b, String deviceFlag) {
		ActionFlagUtil.assertTrue(update(
				new DeviceListDO().withVppMode(b),
				Wrappers.<DeviceListDO>query().eq("device_name", deviceFlag)
		));
	}

	@Override
	@DSTransactional
	public Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo) {
		DeviceListDO deviceListDO = this.getById(deviceSyncStateVo.getDeviceId());
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);

		DeviceStatusEnum deviceStatusEnum;
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		// 没有首次安装时间，就是未知
		if (deviceListDO.getFirstInstall() == 0) {
			deviceStatusEnum = DeviceStatusEnum.UNKNOWN;
		} else {
			String wifiSn = deviceListDO.getWifiSn();
			deviceStatusEnum = specificServService.checkDeviceStatus(wifiSn);
		}

		AtomicInteger state = new AtomicInteger(deviceStatusEnum.getDbCode());
		deviceListDO.setState(state.get());
		deviceListDO.setUpdateTime(LocalDateTime.now());
		if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE)) {
			OperationUtil
					.of(specificServService.getDeviceAssignProperty(
							deviceListDO.getDeviceName(),
							"sys_run_mode"
					))
					.ifPresentOrElse(
							dict -> {
								try {
									int stateVal = NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue();
									log.info("device: {}, updateState is: {}", deviceSyncStateVo.getDeviceId(), stateVal);
									deviceListDO.setState(stateVal);
								} catch (Exception e) {
									deviceListDO.setState(DeviceStatusEnum.ONLINE.getCode());
								}
							},
							() -> deviceListDO.setState(DeviceStatusEnum.OFFLINE.getDbCode())
					);
		}
		ActionFlagUtil.assertTrue(updateById(deviceListDO));
		return deviceListDO.getState();
	}

	@Override
	@DSTransactional
	public DeviceStatusAndSocDTO syncDeviceStateAndSoc(DeviceSyncStateVo deviceSyncStateVo) {
		DeviceStatusAndSocDTO deviceStatusAndSocDTO = new DeviceStatusAndSocDTO();
		DeviceListDO deviceListDO = this.getById(deviceSyncStateVo.getDeviceId());
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceListDO);

		deviceStatusAndSocDTO.setDeviceName(deviceListDO.getDeviceName());
		DeviceStatusEnum deviceStatusEnum;
		SpecificServService specificServService = strategyService.chooseSpecificServ(deviceListDO);
		// 没有首次安装时间，就是未知
		if (deviceListDO.getFirstInstall() == 0) {
			deviceStatusEnum = DeviceStatusEnum.UNKNOWN;
		} else {
			String wifiSn = deviceListDO.getWifiSn();
			deviceStatusEnum = specificServService.checkDeviceStatus(wifiSn);
		}

		AtomicInteger state = new AtomicInteger(deviceStatusEnum.getDbCode());
		deviceListDO.setState(state.get());
		deviceListDO.setUpdateTime(LocalDateTime.now());
		deviceStatusAndSocDTO.setBatSoc(null);
		if (deviceStatusEnum.equals(DeviceStatusEnum.ONLINE)) {
			OperationUtil
					.of(specificServService.getDeviceAssignProperty(
							deviceListDO.getDeviceName(),
							"sys_run_mode"
					))
					.ifPresentOrElse(
							dict -> {
								try {
									int stateVal = NumberUtil.round(String.valueOf(dict.get("sys_run_mode")), 0).intValue();
									log.info("device: {}, updateState is: {}", deviceSyncStateVo.getDeviceId(), stateVal);
									deviceListDO.setState(stateVal);
								} catch (Exception e) {
									deviceListDO.setState(DeviceStatusEnum.ONLINE.getCode());
								}
							},
							() -> deviceListDO.setState(DeviceStatusEnum.OFFLINE.getDbCode())
					);
			querySocForOnlineDevice(deviceListDO);
			deviceStatusAndSocDTO.setBatSoc(deviceListDO.getBatSoc());
		}
		deviceStatusAndSocDTO.setState(deviceListDO.getState());
		ActionFlagUtil.assertTrue(updateById(deviceListDO));
		return  deviceStatusAndSocDTO;
	}

	private void querySocForOnlineDevice(DeviceListDO deviceListDO) {
		try {
			TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService
					.chooseTimeSeriesDatabaseService(deviceListDO);

			// 获取最近点位数据
			Dict lastPointDict = timeSeriesDatabaseService.lastDataPoint(
					deviceListDO.getDeviceSn(),
					new LinkedList<>(ListUtil.of(TsdbMetricsConstants.BAT_SOC)),
					DateUtil.currentSeconds()
			);

			// 检查数据完整性
			if (lastPointDict.values().stream().anyMatch(Objects::isNull)) {
				log.warn("设备 {} SOC数据存在Null: {}", deviceListDO.getDeviceName(), JSONUtil.toJsonStr(lastPointDict));
				deviceListDO.setBatSoc(null);
			}

			String batSoc = lastPointDict.getStr(TsdbMetricsConstants.BAT_SOC);
			if (StrUtil.isNotBlank(batSoc)) {
				deviceListDO.setBatSoc(NumberUtil.div(new BigDecimal(batSoc), new BigDecimal("100"), 2, RoundingMode.HALF_UP));
			} else {
				deviceListDO.setBatSoc(null);
			}
		} catch (Exception e) {
			log.warn("查询设备 {} SOC失败: {}", deviceListDO.getDeviceName(), e.getMessage());
		}
	}

	@Override
	public DeviceListDO getDeviceInfoByDeviceName(String deviceName) {
		DeviceListDO deviceListDO = getOne(Wrappers.<DeviceListDO>lambdaQuery().eq(DeviceListDO::getDeviceName, deviceName));
		if (null == deviceListDO) {
			throw new CustomException(ExceptionEnum.DEVICE_NOT_EXIST);
		}
		return deviceListDO;
	}

	@Override
	public void updateDeviceAlias(DeviceAliasUpdateVo deviceAliasUpdateVo) {
		DeviceListDO deviceInfo = getDeviceById(Long.parseLong(deviceAliasUpdateVo.getId()));
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceInfo);
		authCenterResClient.updUserResourceRemark(deviceAliasUpdateVo.getId(),
				UserResourceRemarkDTO.builder()
						.remark(deviceAliasUpdateVo.getAlias())
						.build());

		String deviceName = deviceInfo.getDeviceName();
		String beforeAlias = deviceInfo.getAlias();
		String afterAlias = deviceAliasUpdateVo.getAlias();
		// tag
		OperatingRecordUtil
				.log(
						RecordContentConstants.UPDATE_DEVICE_ALIAS,
						MapUtil.<String, String>builder().put("deviceName", deviceName).put("beforeAlias", beforeAlias)
								.put("afterAlias", afterAlias).build(),
						EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
				);
	}

	@Override
	public void updateDeviceDescription(DeviceDescUpdateVo deviceDescUpdateVo) {
		DeviceListDO deviceInfo = getDeviceById(Long.parseLong(deviceDescUpdateVo.getId()));
		com.weihengtech.utils.BeanUtil.assertNotNull(deviceInfo);
		authCenterResClient.upResourceDesc(deviceDescUpdateVo.getId(),
				ResourceDescDTO.builder()
						.desc(deviceDescUpdateVo.getDesc())
						.build());

		String deviceName = deviceInfo.getDeviceName();
		String afterDesc = deviceDescUpdateVo.getDesc();

		// tag
		OperatingRecordUtil
				.log(
						RecordContentConstants.UPDATE_DEVICE_DESC,
						MapUtil.<String, String>builder().put("deviceName", deviceName).put("afterDesc", afterDesc).build(),
						EnumConstants.RecordModule.DEVICE_OPERATION.getCode()
				);
	}
}
