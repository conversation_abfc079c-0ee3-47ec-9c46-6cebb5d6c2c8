package com.weihengtech.service.device;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.IService;
import com.weihengtech.pojo.bos.device.DeviceListPageBo;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dtos.device.*;
import com.weihengtech.pojo.dtos.device.info.DeviceInformationDTO;
import com.weihengtech.pojo.dtos.other.PageInfoDTO;
import com.weihengtech.pojo.dtos.parser.ParseErrorDTO;
import com.weihengtech.pojo.vos.device.*;
import com.weihengtech.pojo.vos.tsdb.TsdbParseErrorVo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceListService extends IService<DeviceListDO> {

	/**
	 * 分页查询设备列表
	 *
	 * @param param 分页参数
	 * @return 分月数据
	 */
	PageInfoDTO<DeviceListPageDTO> pageDeviceList(DeviceListPageBo param, String redisSubName);

	/**
	 * 操作设备状态
	 *
	 * @param deviceStateHandleVO 选择的操作选项
	 */
	void handleDeviceState(DeviceStateHandleVO deviceStateHandleVO);

	/**
	 * 模糊查询设备名
	 *
	 * @param deviceName 设备名
	 * @return 模糊匹配的设备名列表
	 */
	List<String> likeDeviceName(String deviceName);

	/**
	 * 更新设备的vpp模式
	 *
	 * @param b          false: 关闭 true: 开启
	 * @param deviceFlag 设备标识
	 */
	void updateVppMode(boolean b, String deviceFlag);

	/**
	 * 同步设备状态
	 *
	 * @param deviceSyncStateVo 要同步的设备id
	 * @return 同步后的状态
	 */
	Integer syncDeviceState(DeviceSyncStateVo deviceSyncStateVo);

	/**
	 * 同步设备状态和SOC
	 *
	 * @param deviceSyncStateVo 要同步的设备id
	 * @return 同步后的状态和SOC
	 */
	DeviceStatusAndSocDTO syncDeviceStateAndSoc(DeviceSyncStateVo deviceSyncStateVo);

	/**
	 * 根据设备标识查询设备对象
	 *
	 * @param deviceName 设备标识
	 * @return 设备对象
	 */
	DeviceListDO getDeviceInfoByDeviceName(String deviceName);

	/**
	 * 更新设备别名
	 *
	 * @param deviceAliasUpdateVo 设备别名入参
	 */
	void updateDeviceAlias(DeviceAliasUpdateVo deviceAliasUpdateVo);

	/**
	 * 更新设备描述
	 *
	 * @param deviceDescUpdateVo 设备描述入参
	 */
	void updateDeviceDescription(DeviceDescUpdateVo deviceDescUpdateVo);

	/**
	 * 批量新增设备列表
	 *
	 * @param batchNewDeviceVo 批量deviceSn
	 * @return 已经存在的deviceSn
	 */
	BatchNewDeviceDto batchNewDevice(BatchNewDeviceVo batchNewDeviceVo);

	/**
	 * 同步设备版本信息
	 *
	 * @param deviceInformationDTO 设备属性信息
	 * @param deviceListDO  设备实例
	 * @return 同步后数据
	 */
	Pair<DeviceSyncVersionDto, Boolean> syncDeviceVersionByReadResult(
			DeviceInformationDTO deviceInformationDTO, DeviceListDO deviceListDO
	);

	/**
	 * 模糊查询wifiSn
	 *
	 * @param wifiSn     wifiSn
	 * @return wifiSnList
	 */
	List<String> likeWifiSn(String wifiSn);

	/**
	 * @param alias      备注
	 * @return aliasList
	 */
	List<String> likeAlias(String alias);

	/**
	 * @param desc      描述
	 * @return aliasList
	 */
	List<String> likeDesc(String desc);

	/**
	 * 解析错误码
	 *
	 * @param tsdbParseErrorVo 错误码类型与值
	 * @return 解析结果
	 */
	ParseErrorDTO parseError(TsdbParseErrorVo tsdbParseErrorVo);

	/**
	 * 获取代理商账号
	 */
	String getAgentId(String deviceId);

	/**
	 * 更新设备国家
	 *
	 * @param countryId    国家id
	 * @param deviceSnList 设备sn列表
	 */
	void updateDeviceCountry(Integer countryId, List<String> deviceSnList);

	/**
	 * 根据wifi棒序列号获取设备信息
	 *
	 * @param wifiSn wifi棒序列号
	 * @return 设备实例
	 */
	DeviceListDO getDeviceBeanByWifiSn(String wifiSn);

	/**
	 * 查询AU的设备
	 * @return 所有的AU设备sn
	 */
	List<String> listAuDeviceSn();

	/**
	 * 获取现在绑定的设备列表
	 * @param wifiSn wifi棒
	 * @return 设备列表
	 */
	List<DeviceListDO> getNowBindDeviceList(String wifiSn);

	/**
	 * 其他绑定的设备列表
	 * @param wifiSn wifi棒
	 * @param deviceName 设备名
	 * @return 设备列表
	 */
	List<DeviceListDO> getOtherBindDeviceList(String wifiSn, String deviceName);

	/**
	 * 查询相同数据中心设备
	 * @param deviceId 设备id
	 * @return 相同数据中心的设备
	 */
    DeviceListDO querySameDatacenterDevice(Long deviceId);

	/**
	 * 保存新设备
	 *
	 * @param deviceSn 设备SN
	 * @param bindParam WiFi SN
	 * @param datacenterId 数据中心
	 * @return 保存结果
	 */
    Long saveDeviceInfo(String deviceSn, NetBindDeviceVO bindParam, int datacenterId);

	/**
	 * 更新设备信息
	 *
	 * @param deviceInfo deviceInfo
	 * @param bindParam 绑定入参
	 */
    void updateDeviceInfo(DeviceListDO deviceInfo, NetBindDeviceVO bindParam);

	/**
	 * 根据wifi sn批量获取设备Id
	 *
	 * @param wifiSnList wifi sn
	 * @return Id
	 */
	List<String> getBatchIdsByWifiSn(Collection<String> wifiSnList);

	/**
	 * 根据Id获取设备信息
	 *
	 * @param id id
	 * @return 设备
	 */
	DeviceListDO getDeviceById(Long id);

	/**
	 * 根据Id批量获取设备信息
	 *
	 * @param idList id
	 * @param isNeedExt 是否需要查询扩展信息
	 * @return 设备
	 */
	List<DeviceListDO> getDeviceByIds(Collection<String> idList, Boolean isNeedExt);

	/**
	 * 根据数据中心查询设备列表
	 *
	 * @param datacenter 数据中心
	 * @return 设备列表
	 */
	List<DeviceListDO> getDeviceByDatacenter(String datacenter);

	/**
	 * 保存新设备
	 *
	 * @param item 设备信息
	 */
	void saveNewDevice(DeviceListDO item);

	/**
	 * 更新设备
	 *
	 * @param item 设备信息
	 */
	void updDevice(DeviceListDO item);

	/**
	 * 模糊搜索设备Sn
	 *
	 * @param deviceName 设备Sn
	 * @return 设备信息
	 */
	List<DeviceLikeSnDTO> likeDeviceSn(String deviceName);

	/**
	 * 储能设备分页
	 *
	 * @param queryParam 查询参数
	 * @param storageDeviceList 储能设备日志参数
	 * @return 设备列表
	 */
	PageInfoDTO<DeviceListPageDTO> storageList(DeviceListPageBo queryParam, String storageDeviceList);

	/**
	 * sn模糊搜索分页
	 *
	 * @param pageVo sn
	 * @return 分页数据
	 */
	PageInfoDTO<DeviceLikeSnDTO> snLikePage(DeviceLikePageVo pageVo);

	/**
	 * 设备信息统计
	 *
	 * @return 统计信息
	 */
    DeviceStatisticsDTO statistics();

	/**
	 * 更改设备详细地址
	 *
	 * @param deviceAddressUpdateVo 设备地址更新入参
	 */
	void updateDeviceAddress(DeviceAddressUpdateVo deviceAddressUpdateVo);
}
