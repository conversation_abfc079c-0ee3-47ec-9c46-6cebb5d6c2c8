package com.weihengtech.service.systeminfo.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.weihengtech.auth.dto.BindBatchReqDTO;
import com.weihengtech.auth.dto.BindInfoDTO;
import com.weihengtech.auth.dto.BindQueryReqDTO;
import com.weihengtech.auth.dto.BindReqDTO;
import com.weihengtech.auth.model.dto.UserResDTO;
import com.weihengtech.auth.rpc.AuthCenterClient;
import com.weihengtech.auth.rpc.AuthCenterResClient;
import com.weihengtech.auth.utils.UserInfoUtil;
import com.weihengtech.common.exceptions.CustomException;
import com.weihengtech.common.exceptions.ExceptionEnum;
import com.weihengtech.consts.RoleConstants;
import com.weihengtech.dao.systeminfo.SystemInfoMapper;
import com.weihengtech.enums.device.DeviceSaveTimeEnum;
import com.weihengtech.enums.device.SeriesEnum;
import com.weihengtech.enums.system.StepEnum;
import com.weihengtech.pojo.dos.device.DeviceListDO;
import com.weihengtech.pojo.dos.systeminfo.SystemDeviceRelDO;
import com.weihengtech.pojo.dos.systeminfo.SystemInfoDO;
import com.weihengtech.pojo.vos.systeminfo.SettingTransferVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoQueryVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoSaveResVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoSaveVO;
import com.weihengtech.pojo.vos.systeminfo.SystemInfoUpdVO;
import com.weihengtech.service.device.DeviceListService;
import com.weihengtech.service.systeminfo.SystemDeviceRelService;
import com.weihengtech.service.systeminfo.SystemInfoService;
import com.weihengtech.service.other.TuyaDatacenterService;
import com.weihengtech.utils.ActionFlagUtil;
import com.weihengtech.utils.RotUtil;
import com.weihengtech.utils.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 系统信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-14
 */
@Service
@Slf4j
public class SystemInfoServiceImpl extends ServiceImpl<SystemInfoMapper, SystemInfoDO> implements SystemInfoService {

    @Resource
    private SystemInfoMapper systemInfoMapper;
    @Autowired
    private SnowFlakeUtil snowFlakeUtil;
    @Autowired
    private SystemDeviceRelService systemDeviceRelService;
    @Autowired
    private TuyaDatacenterService tuyaDatacenterService;
    @Resource
    private AuthCenterClient authCenterClient;
    @Resource
    private AuthCenterResClient authCenterResClient;
    @Resource
    private DeviceListService deviceListService;

    @Override
    public SystemInfoSaveResVO saveInfo(SystemInfoSaveVO saveVO) {
        SystemInfoSaveVO.checkParams(saveVO);
        long userId = Long.parseLong(UserInfoUtil.currentUserId());
        long id = snowFlakeUtil.generateId();
        int curStep = StepEnum.STEP_1.getStepNum();
        SystemInfoDO systemInfoDO = SystemInfoDO.builder()
                .id(id)
                .installerId(userId)
                .currentStep(curStep)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        BeanUtils.copyProperties(saveVO, systemInfoDO);
        boolean saveRes = save(systemInfoDO);
        ActionFlagUtil.assertTrue(saveRes);
        return SystemInfoSaveResVO.builder()
                .id(id)
                .currentStep(curStep)
                .build();
    }

    @Override
    public SystemInfoDO getSysInfo(Long id) {
        SystemInfoDO infoDO = systemInfoMapper.getById(id);
        if (infoDO == null) {
            return null;
        }
        // 根据用户Id获取用户信息
        List<UserResDTO> userInfo = authCenterClient.list(String.valueOf(infoDO.getInstallerId()), null);
        ActionFlagUtil.assertTrue(userInfo.size() == 1);
        infoDO.setInstallerAccount(userInfo.get(0).getEmail());
        List<SystemDeviceRelDO> deviceList = systemDeviceRelService.listBySystemId(id);
        if (CollUtil.isEmpty(deviceList)) {
            return infoDO;
        }
        List<String> deviceSnList = deviceList.stream()
                .map(SystemDeviceRelDO::getDeviceSn)
                .collect(Collectors.toList());
        List<String> confirmedDeviceSnList = deviceList.stream()
                .filter(i -> 1 == i.getIsCheck())
                .map(SystemDeviceRelDO::getDeviceSn)
                .collect(Collectors.toList());
        List<String> storageEnergyList = deviceList.stream()
                .filter(i -> SeriesEnum.isEnergyStorageDevice(i.getResourceSeries()))
                .map(SystemDeviceRelDO::getDeviceSn)
                .collect(Collectors.toList());
        infoDO.setDeviceSnList(deviceSnList);
        infoDO.setConfirmedDeviceSnList(confirmedDeviceSnList);
        infoDO.setEnergyStorageSnList(storageEnergyList);
        return infoDO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updById(SystemInfoUpdVO updVO) {
        updVO.checkParams();
        // 查询已有系统
        SystemInfoDO exists = getById(updVO.getId());
        ActionFlagUtil.assertTrue(exists != null);
        // 构建更新对象
        SystemInfoDO target = new SystemInfoDO();
        BeanUtils.copyProperties(updVO, target);
        if (updVO.getDatacenterId() != null && !updVO.getDatacenterId().equals(exists.getDatacenterId())) {
            // 判断是否相同phone code，涂鸦数据中心是按照phone code划分，只更改地区如果数据中心没变的话，无需退回操作
            boolean isSame = tuyaDatacenterService.isSamePhoneCode(updVO.getDatacenterId(), exists.getDatacenterId());
            if (!isSame) {
                /* 如果数据中心变更，则需执行以下退回步骤：
                 * 1、系统步骤回退->1
                 * 2、解除设备绑定关系
                 * 3、清除设备主表记录
                 * 4、清除系统设备关联关系
                 * 重新配网会生成新的wifi sn，但deviceSn不变，绑定流程中会兼容已存在deviceSn记录的场景，所以deviceId不变，则绑定关系不变。
                 * 同时添加设备流程中兼容已存在设备系统关联的场景，会自动重置预设检查状态，所以系统设备关联关系不变。
                 * 综上，只需系统步骤退回即可。
                 */
                unBindDevice(exists);
                target.setCurrentStep(StepEnum.STEP_1.getStepNum());
            }
        }
        target.setUpdateTime(new Date());
        // 更新系统详情
        updateById(target);
        // 更新倒计时
        if (updVO.getTransferTime() != null) {
            updCountDownTime(exists.getId(), updVO);
        }

        // 根据详细地址是否改变决定是否更新设备的详细地址
        if (updVO.getAddress() != null && !updVO.getAddress().equals(exists.getAddress())) {
            // 查询当前关联设备列表
            List<SystemDeviceRelDO> curList = systemDeviceRelService.listBySystemId(updVO.getId());

            // 查询设备列表
            Collection<DeviceListDO> deviceListDOS = deviceListService.listByIds(curList.stream()
                    .map(SystemDeviceRelDO::getDeviceId)
                    .collect(Collectors.toList()));

            // 更新设备详细地址
            deviceListDOS.forEach(i -> {
                i.setAddress(updVO.getAddress());
            });
            deviceListService.updateBatchById(deviceListDOS);
        }

        // 更新设备状态
        if (updVO.getConfirmDeviceId() != null) {
            // 查询当前关联设备列表
            List<SystemDeviceRelDO> curList = systemDeviceRelService.listBySystemId(updVO.getId());
            if (CollUtil.isEmpty(curList)) {
                return;
            }
            // 如果当前更新的设备为仅剩的未确认的设备，则更新系统步骤
            List<SystemDeviceRelDO> unCheckedList = curList.stream()
                    .filter(i -> 1 != i.getIsCheck())
                    .filter(i -> SeriesEnum.isEnergyStorageDevice(i.getResourceSeries()))
                    .collect(Collectors.toList());
            if (unCheckedList.size() == 1 &&
                    unCheckedList.get(0).getId().equals(updVO.getConfirmDeviceId()) &&
                    StepEnum.STEP_3.getStepNum() != exists.getCurrentStep()) {
                target.setCurrentStep(StepEnum.STEP_3.getStepNum());
                updateById(target);
            }
            // 更新设备状态
            SystemDeviceRelDO upd = SystemDeviceRelDO.builder()
                    .id(updVO.getConfirmDeviceId())
                    .isCheck(1)
                    .build();
            ActionFlagUtil.assertTrue(systemDeviceRelService.updateById(upd));
        }
    }

    /**
     * 解绑系统关联的设备
     *
     * @param exists 当前系统
     */
    private void unBindDevice(SystemInfoDO exists) {
        // 批量解绑当前设备
        LambdaQueryWrapper<SystemDeviceRelDO> wrapper = Wrappers.<SystemDeviceRelDO>lambdaQuery()
                .eq(SystemDeviceRelDO::getSystemId, exists.getId());
        List<SystemDeviceRelDO> deviceList = systemDeviceRelService.list(wrapper);
        authCenterResClient.unbindInstaller(BindReqDTO.builder()
                .userId(UserInfoUtil.currentUserId())
                .roleId(UserInfoUtil.currentUserRoleId())
                .roleCategory(UserInfoUtil.currentUserRoleCategory())
                .resourceCodes(deviceList.stream()
                                .map(SystemDeviceRelDO::getDeviceSn)
                                .collect(Collectors.toList())).build());
        // 清除系统关联设备
        for (SystemDeviceRelDO device : deviceList) {
            systemDeviceRelService.removeRel(exists.getInstallerId(), device.getDeviceId());
        }
    }

    /**
     * 更新绑定倒计时
     *
     * @param id 系统Id
     * @param updVO 倒计时
     */
    private void updCountDownTime(Long id, SystemInfoUpdVO updVO) {
        SystemInfoDO systemInfo = systemInfoMapper.getById(id);
        List<SystemDeviceRelDO> deviceList = systemInfo.getDeviceList();
        if (CollUtil.isEmpty(deviceList)) {
            return;
        }
        // 先查询当前设备绑定的安装商
        List<String> idList = deviceList.stream()
                .map(SystemDeviceRelDO::getDeviceId)
                .map(String::valueOf)
                .collect(Collectors.toList());
        BindQueryReqDTO req = BindQueryReqDTO.builder()
                .resourceIds(idList)
                .roleCategory(RoleConstants.ROLE_INSTALLER)
                .build();
        List<BindInfoDTO> bindInfoList = authCenterResClient.getBatchBindInfo(req);
        if (CollUtil.isEmpty(bindInfoList)) {
            log.info("bindInfoList is empty: {}", idList);
            return;
        }
        Map<String, String> bindMap = bindInfoList.stream()
                .collect(Collectors.toMap(BindInfoDTO::getResourceId, BindInfoDTO::getUserId));

        for (SystemDeviceRelDO deviceInfo : deviceList) {
            if (updVO.getSaveDeviceTime() != null && DeviceSaveTimeEnum.NEVER.getCode() == updVO.getSaveDeviceTime()) {
                // 安装商绑定关系不保留
                authCenterResClient.unbindInstaller(BindReqDTO.builder()
                        .userId(bindMap.get(String.valueOf(deviceInfo.getDeviceId())))
                        .resourceId(String.valueOf(deviceInfo.getDeviceId()))
                        .roleId(RoleConstants.ROLE_INSTALLER)
                        .roleCategory(RoleConstants.ROLE_INSTALLER)
                        .build());
                continue;
            }

            BindReqDTO item = BindReqDTO.builder()
                    .userId(String.valueOf(systemInfo.getInstallerId()))
                    .roleId(RoleConstants.ROLE_INSTALLER)
                    .resourceId(String.valueOf(deviceInfo.getDeviceId()))
                    .build();
            if (updVO.getSaveDeviceTime() == null || DeviceSaveTimeEnum.DAY_3.getCode() == updVO.getSaveDeviceTime()) {
                item.setBindTime(updVO.getTransferTime().getTime());
            }
            authCenterResClient.bindInstaller(item);
        }
    }

    @Override
    public List<SystemInfoDO> listByAccount(Long installerId) {
        SystemInfoQueryVO queryParam = SystemInfoQueryVO.builder()
                .installerId(installerId)
                .build();
        List<SystemInfoDO> list = systemInfoMapper.listByCondition(queryParam);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        // 根据用户Id获取用户信息
        String ids = list.stream()
                .map(SystemInfoDO::getInstallerId)
                .map(String::valueOf)
                .collect(Collectors.joining(Constants.COMMA));
        List<UserResDTO> userList = authCenterClient.list(ids, null);
        Map<String, String> userMap = userList.stream()
                .collect(Collectors.toMap(UserResDTO::getId, UserResDTO::getEmail));
        List<SystemInfoDO> resList = new ArrayList<>(list.size());
        list.forEach(k -> {
            List<String> deviceSnList = k.getDeviceList().stream()
                    .map(SystemDeviceRelDO::getDeviceSn)
                    .collect(Collectors.toList());
            List<String> confirmedDeviceSnList = k.getDeviceList().stream()
                    .filter(i -> 1 == i.getIsCheck())
                    .map(SystemDeviceRelDO::getDeviceSn)
                    .collect(Collectors.toList());
            k.setDeviceSnList(deviceSnList);
            k.setConfirmedDeviceSnList(confirmedDeviceSnList);
            k.setInstallerAccount(userMap.get(String.valueOf(k.getInstallerId())));
        });
        // 排序，未完成、已完成，各自分组中时间倒排
        List<SystemInfoDO> unFinishedList = list.stream()
                .filter(i -> i.getTransferTime() == null)
                .sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()))
                .collect(Collectors.toList());
        List<SystemInfoDO> finishedList = list.stream()
                .filter(i -> i.getTransferTime() != null)
                .sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()))
                .collect(Collectors.toList());
        resList.addAll(unFinishedList);
        resList.addAll(finishedList);
        return resList;
    }

    @Override
    public boolean backStepSystem(Long installerId, Long deviceId) {
        List<SystemInfoDO> sysList = systemInfoMapper.getSystemByUserAndDevice(installerId, deviceId);
        if (CollUtil.isEmpty(sysList)) {
            return true;
        }
        sysList.forEach(i -> {
            SystemInfoDO upd = SystemInfoDO.builder()
                    .id(i.getId())
                    .currentStep(StepEnum.STEP_1.getStepNum())
                    .build();
            boolean res = updateById(upd);
            ActionFlagUtil.assertTrue(res);
        });
        return true;
    }

    @Override
    public String qrCodeEncryption(SettingTransferVO dto) {
        long timestamp = System.currentTimeMillis() / 1000 / 60;
        int offset = new Random().nextInt(9);
        return offset + RotUtil.encode(timestamp + JSONObject.toJSONString(dto), offset);
    }

    @Override
    public List<String> getValidDeviceList(String installerId, List<String> deviceIdList) {
        if (CollUtil.isEmpty(deviceIdList)) {
            return Collections.emptyList();
        }
        List<SystemInfoDO> list = systemInfoMapper.listByCondition(SystemInfoQueryVO.builder()
                .installerId(Long.parseLong(installerId)).build());
        if (CollUtil.isEmpty(list)) {
            return deviceIdList;
        }
        // 过滤没有关联设备的系统
        List<SystemInfoDO> sysList = list.stream()
                .filter(i -> CollUtil.isNotEmpty(i.getDeviceList()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(sysList)) {
            return deviceIdList;
        }
        List<String> resList = new ArrayList<>();
        /*
         * 1、找出关联当前设备的系统
         * 2、如果有多个，则需以最新的系统为准
         * 3、如果有转移时间，则认为设备合法，安装商的设备列表应该展示
         */
        for (String deviceId : deviceIdList) {
            SystemInfoDO validSystemInfo = sysList.stream()
                    .filter(i -> {
                        Set<String> set = i.getDeviceList().stream()
                                .map(SystemDeviceRelDO::getDeviceId)
                                .map(String::valueOf)
                                .collect(Collectors.toSet());
                        return set.contains(deviceId);
                    }).max((o1, o2) -> (int) (o1.getUpdateTime().getTime() - o2.getUpdateTime().getTime()))
                    .orElse(null);
            if (validSystemInfo != null && validSystemInfo.getTransferTime() == null) {
                continue;
            }
            resList.add(deviceId);
        }
        return resList;
    }

    @Override
    public void removeUnFinishedSystem(Long id) {
        SystemInfoDO infoDO = systemInfoMapper.getById(id);
        if (infoDO == null) {
            return;
        }

        // 如果处于转移系统步骤，则不删除
        if (infoDO.getCurrentStep() == StepEnum.STEP_4.getStepNum()) {
            return;
        }

        // 如果处于添加设备之后的步骤，则需解绑设备
        if (infoDO.getCurrentStep() > StepEnum.STEP_1.getStepNum()) {
            unBindDevice(infoDO);
        }

        removeById(id);
    }
}
